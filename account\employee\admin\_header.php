<?php
session_start();
include '../../../connection/dbconnect.php';
require 'inc/inc.function.php';

// Redirect to login.php if session variables are not set
if (empty($_SESSION['accountType']) || empty($_SESSION['id'])) {
    header("Location: ../../../login.php");
    exit();
}

// Generate a CSRF token if it doesn't exist or has expired
if (empty($_SESSION['csrf_token']) || time() > $_SESSION['csrf_token_expiration']) {
    $_SESSION['csrf_token'] = bin2hex(random_bytes(32));
    $_SESSION['csrf_token_expiration'] = time() + 3600; // 1-hour validity
}

$id = $_SESSION['id'];
$accType = $_SESSION['accountType'];

// Combined query to fetch operator info and account status
$sql_global = 'SELECT
            i.firstname, i.middlename, i.lastname, i.extname,
            a.accType, a.email, a.username
        FROM
            operator_info i
        INNER JOIN
            operator_account a
        ON
            i.user_id = a.id
        WHERE
            i.user_id = :id';

$stmt_global = $pdo->prepare($sql_global);
$stmt_global->bindParam(':id', $id, PDO::PARAM_INT);
if (!$stmt_global->execute()) {
    error_log("Error executing combined query: " . implode(", ", $stmt_global->errorInfo()));
    die("An error occurred. Please try again later.");
}

$row_global = $stmt_global->fetch(PDO::FETCH_ASSOC);
if (!$row_global) {
    die("Error: User not found.");
}

if ($row_global['accType'] === $accType || $row_global['accType'] === "admin" || $row_global['accType'] === "mtho staff") {
    $name = trim($row_global['firstname'] . ' ' . $row_global['middlename'] . ' ' . $row_global['lastname'] . ' ' . $row_global['extname']);
    $email = $row_global['email'];
} else {
    header("Location: ../../../login.php");
    exit;
}

// Restrictions
if ($accType === "mtho staff") {
    $hideDiv = "hidden";
} else {
    $hideDiv = "";
}

$notif = getNotifications($pdo);

// Helper function to generate breadcrumb HTML
function generateBreadcrumb($items = array())
{
    $html = '
    <!-- Breadcrumb -->
    <nav class="flex mb-4" aria-label="Breadcrumb">
        <ol class="inline-flex items-center space-x-1 md:space-x-2">
            <li class="inline-flex items-center">
                <a href="home.php" class="inline-flex items-center text-sm font-medium text-gray-700 hover:text-blue-600">
                    <svg class="w-3 h-3 mr-1.5" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="currentColor" viewBox="0 0 20 20">
                        <path d="m19.707 9.293-2-2-7-7a1 1 0 0 0-1.414 0l-7 7-2 2a1 1 0 0 0 1.414 1.414L2 10.414V18a2 2 0 0 0 2 2h3a1 1 0 0 0 1-1v-4a1 1 0 0 1 1-1h2a1 1 0 0 1 1 1v4a1 1 0 0 0 1 1h3a2 2 0 0 0 2-2v-7.586l.293.293a1 1 0 0 0 1.414-1.414Z"/>
                    </svg>
                    Home
                </a>
            </li>';

    foreach ($items as $item) {
        $html .= '
            <li>
                <div class="flex items-center">
                    <svg class="w-3 h-3 text-gray-400 mx-1" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 6 10">
                        <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="m1 9 4-4-4-4"/>
                    </svg>';

        if (isset($item['url'])) {
            $html .= '
                    <a href="' . $item['url'] . '" class="ml-1 text-sm font-medium text-gray-700 hover:text-blue-600">' . $item['title'] . '</a>';
        } else {
            $html .= '
                    <span class="ml-1 text-sm font-medium text-gray-500">' . $item['title'] . '</span>';
        }

        $html .= '
                </div>
            </li>';
    }

    $html .= '
        </ol>
    </nav>';

    return $html;
}

// Function to automatically generate breadcrumb based on current page
function getAutoBreadcrumb()
{
    $current_page = basename($_SERVER['PHP_SELF'], '.php');

    // Skip for home page
    if ($current_page == 'home') {
        return generateBreadcrumb();
    }

    // Map of page names to readable titles
    $page_titles = [
        'personnel' => 'Personnel',
        'operator' => 'Operators',
        'transaction-pending' => 'Pending Transactions',
        'transaction-approved' => 'Approved Transactions',
        'transaction-departed' => 'Departed Transactions',
        'transaction-monitoring' => 'Monitoring',
        'port' => 'Port Lists',
        'logs' => 'Logs',
        'system-maintenance' => 'System Maintenance',
        'transaction' => 'Transaction Summary',
        'demographic' => 'Demographic Summary',
        'myaccount' => 'My Account',
        'settings' => 'Settings'
    ];

    // Get the title for the current page
    $title = isset($page_titles[$current_page]) ? $page_titles[$current_page] : ucwords(str_replace('-', ' ', $current_page));

    return generateBreadcrumb([['title' => $title]]);
}

// Badge functions remain unchanged
function getBadge($count)
{
    return $count > 0 ? '<span class="inline-flex items-center justify-center w-4 h-4 ms-2 text-xs font-semibold text-red-800 bg-red-200 rounded-full">'
        . $count . '</span>' : '';
}

$badgePending = getBadge($notif['Pending'] ?? 0);
$badgeApproved = getBadge($notif['Approved'] ?? 0);
$badgeWaiting = getBadge($notif['Waiting'] ?? 0);


?>



<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <link rel="icon" type="image/x-icon" href="../../../components/img/Logo.png" />
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
    <link href="https://cdn.jsdelivr.net/npm/flowbite@2.5.2/dist/flowbite.min.css" rel="stylesheet" />
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/all.min.css" />
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <title>Admin Dashboard | Calaguas TDF Portal</title>
    <style>
        /* Custom scrollbar */
        ::-webkit-scrollbar {
            width: 8px;
            height: 8px;
        }

        ::-webkit-scrollbar-track {
            background: #f1f1f1;
        }

        ::-webkit-scrollbar-thumb {
            background: #c5c5c5;
            border-radius: 4px;
        }

        ::-webkit-scrollbar-thumb:hover {
            background: #a8a8a8;
        }

        /* Active sidebar item */
        .sidebar-active {
            background-color: #f3f4f6;
            color: #1d4ed8 !important;
            font-weight: 600;
        }

        .dark .sidebar-active {
            background-color: #374151;
            color: #ffffff !important;
        }

        /* Transition effects */
        .hover-transition {
            transition: all 0.3s ease;
        }

        /* Card hover effects */
        .card-hover:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
        }

        /* DataTables custom styling */
        .dataTable-wrapper .dataTable-top {
            display: flex;
            flex-direction: column;
            gap: 1rem;
            padding: 1rem 0;
        }

        @media (min-width: 768px) {
            .dataTable-wrapper .dataTable-top {
                flex-direction: row;
                justify-content: space-between;
                align-items: center;
            }
        }

        .dataTable-wrapper .dataTable-search {
            width: 100%;
            max-width: 16rem;
            margin-bottom: 1rem;
        }

        @media (min-width: 768px) {
            .dataTable-wrapper .dataTable-search {
                margin-bottom: 0;
            }
        }

        .dataTable-wrapper .dataTable-bottom {
            display: flex;
            flex-direction: column;
            gap: 1rem;
            padding: 1rem 0;
        }

        @media (min-width: 768px) {
            .dataTable-wrapper .dataTable-bottom {
                flex-direction: row;
                justify-content: space-between;
                align-items: center;
            }
        }

        .dataTable-wrapper .dataTable-info {
            font-size: 0.875rem;
            color: #6b7280;
        }

        .dataTable-wrapper .dataTable-pagination {
            display: flex;
            gap: 0.25rem;
        }

        .dataTable-wrapper .dataTable-pagination a {
            padding: 0.375rem 0.75rem;
            border-radius: 0.25rem;
            color: #374151;
            text-decoration: none;
            border: 1px solid #d1d5db;
        }

        .dataTable-wrapper .dataTable-pagination a:hover {
            background-color: #f3f4f6;
        }

        .dataTable-wrapper .dataTable-pagination a.active {
            background-color: #3b82f6;
            color: white;
            border-color: #3b82f6;
        }
    </style>
    <script>
        // Function to set active sidebar item based on current page
        document.addEventListener('DOMContentLoaded', function () {
            // Get current page filename
            const path = window.location.pathname;
            const filename = path.substring(path.lastIndexOf('/') + 1);
            const pageName = filename.split('.')[0]; // Remove extension

            // Find the matching sidebar link and add active class
            document.querySelectorAll('#logo-sidebar a').forEach(link => {
                const href = link.getAttribute('href');
                if (href === filename || (pageName === 'home' && href === 'home.php')) {
                    link.classList.add('sidebar-active');
                } else if (pageName.includes('personnel') && href === 'personnel.php') {
                    link.classList.add('sidebar-active');
                } else if (pageName.includes('operator') && href === 'operator.php') {
                    link.classList.add('sidebar-active');
                } else if (pageName.includes('transaction-pending') && href === 'transaction-pending.php') {
                    link.classList.add('sidebar-active');
                } else if (pageName.includes('transaction-approved') && href === 'transaction-approved.php') {
                    link.classList.add('sidebar-active');
                } else if (pageName.includes('transaction-departed') && href === 'transaction-departed.php') {
                    link.classList.add('sidebar-active');
                } else if (pageName.includes('transaction-monitoring') && href === 'transaction-monitoring.php') {
                    link.classList.add('sidebar-active');
                } else if (pageName.includes('port') && href === 'port.php') {
                    link.classList.add('sidebar-active');
                } else if (pageName.includes('logs') && href === 'logs.php') {
                    link.classList.add('sidebar-active');
                } else if (pageName.includes('system-maintenance') && href === 'system-maintenance.php') {
                    link.classList.add('sidebar-active');
                } else if (pageName.includes('transaction') && href === 'transaction.php') {
                    link.classList.add('sidebar-active');
                } else if (pageName.includes('demographic') && href === 'demographic.php') {
                    link.classList.add('sidebar-active');
                } else if (pageName.includes('myaccount') && href === 'myaccount.php') {
                    link.classList.add('sidebar-active');
                }
            });
        });
    </script>
</head>

<body>
    <nav class="fixed top-0 z-50 w-full bg-gradient-to-r from-blue-600 to-blue-800 border-b border-blue-700 shadow-md">
        <div class="px-3 py-3 lg:px-5 lg:pl-3">
            <div class="flex items-center justify-between">
                <div class="flex items-center justify-start rtl:justify-end">
                    <button data-drawer-target="logo-sidebar" data-drawer-toggle="logo-sidebar"
                        aria-controls="logo-sidebar" type="button"
                        class="inline-flex items-center p-2 text-sm text-white rounded-lg sm:hidden hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500">
                        <span class="sr-only">Open sidebar</span>
                        <svg class="w-6 h-6" aria-hidden="true" fill="currentColor" viewBox="0 0 20 20"
                            xmlns="http://www.w3.org/2000/svg">
                            <path clip-rule="evenodd" fill-rule="evenodd"
                                d="M2 4.75A.75.75 0 012.75 4h14.5a.75.75 0 010 1.5H2.75A.75.75 0 012 4.75zm0 10.5a.75.75 0 01.75-.75h7.5a.75.75 0 010 1.5h-7.5a.75.75 0 01-.75-.75zM2 10a.75.75 0 01.75-.75h14.5a.75.75 0 010 1.5H2.75A.75.75 0 012 10z">
                            </path>
                        </svg>
                    </button>
                    <a href="home.php" class="flex ms-2 md:me-24">
                        <img src="../../../components/img/Logo.png" class="h-8 me-3 bg-white rounded-full p-1"
                            alt="Calaguas Logo" />
                        <div class="flex flex-col">
                            <span class="self-center text-xl font-semibold text-white">Calaguas TDF</span>
                            <span class="text-xs text-blue-200">Admin Portal</span>
                        </div>
                    </a>
                </div>
                <div class="flex items-center gap-4">
                    <!-- Notifications -->
                    <div class="relative">
                        <button type="button"
                            class="relative p-2 text-white rounded-lg hover:bg-blue-700 focus:ring-2 focus:ring-blue-500">
                            <span class="sr-only">View notifications</span>
                            <i class="fas fa-bell"></i>
                            <?php if (($notif['Pending'] ?? 0) + ($notif['Approved'] ?? 0) + ($notif['Waiting'] ?? 0) > 0): ?>
                                <div
                                    class="absolute inline-flex items-center justify-center w-5 h-5 text-xs font-bold text-white bg-red-500 rounded-full -top-1 -right-1">
                                    <?= ($notif['Pending'] ?? 0) + ($notif['Approved'] ?? 0) + ($notif['Waiting'] ?? 0) ?>
                                </div>
                            <?php endif; ?>
                        </button>
                    </div>

                    <!-- User menu -->
                    <div class="relative">
                        <button type="button"
                            class="flex items-center gap-2 text-sm text-white rounded-lg hover:bg-blue-700 focus:ring-2 focus:ring-blue-500 p-1"
                            aria-expanded="false" data-dropdown-toggle="dropdown-user">
                            <span class="sr-only">Open user menu</span>
                            <div
                                class="w-8 h-8 rounded-full bg-blue-100 flex items-center justify-center text-blue-600">
                                <i class="fas fa-user-shield"></i>
                            </div>
                            <div class="hidden md:flex md:flex-col">
                                <span class="text-sm font-medium"><?= explode(' ', $name)[0]; ?></span>
                                <span class="text-xs text-blue-200">Administrator</span>
                            </div>
                            <i class="fas fa-chevron-down text-xs"></i>
                        </button>

                        <div id="dropdown-user"
                            class="absolute right-0 z-50 hidden w-60 mt-2 text-base list-none bg-white divide-y divide-gray-100 rounded-lg shadow-lg">
                            <div class="px-4 py-3 bg-blue-50 rounded-t-lg">
                                <p class="text-sm font-semibold text-gray-900"><?= $name; ?></p>
                                <p class="text-sm text-gray-600 truncate">
                                    <?= $email; ?>
                                </p>
                                <div class="mt-2">
                                    <span
                                        class="bg-blue-100 text-blue-800 text-xs font-medium px-2.5 py-0.5 rounded-full">Administrator</span>
                                </div>
                            </div>
                            <ul class="py-2">
                                <li>
                                    <a href="myaccount.php"
                                        class="flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                                        <i class="fas fa-user-circle w-4 h-4 mr-2 text-gray-500"></i>
                                        My Account
                                    </a>
                                </li>
                                <li>
                                    <a href="settings.php"
                                        class="flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                                        <i class="fas fa-cog w-4 h-4 mr-2 text-gray-500"></i>
                                        Settings
                                    </a>
                                </li>
                                <li>
                                    <a href="logout.php"
                                        class="flex items-center px-4 py-2 text-sm text-red-600 hover:bg-red-50">
                                        <i class="fas fa-sign-out-alt w-4 h-4 mr-2 text-red-500"></i>
                                        Logout
                                    </a>
                                </li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </nav>

    <aside id="logo-sidebar"
        class="fixed top-0 left-0 z-40 w-64 h-screen pt-20 transition-transform -translate-x-full bg-white border-r border-gray-200 sm:translate-x-0 shadow-md"
        aria-label="Sidebar">
        <div class="h-full px-3 pb-4 overflow-y-auto bg-white">
            <!-- User Profile Summary -->
            <div class="flex flex-col items-center pb-5 mb-5 border-b border-gray-200">
                <div class="w-20 h-20 mb-3 rounded-full bg-blue-100 flex items-center justify-center text-blue-600">
                    <i class="fas fa-user-shield text-3xl"></i>
                </div>
                <h5 class="mb-1 text-lg font-medium text-gray-900"><?= explode(' ', $name)[0]; ?></h5>
                <span class="text-sm text-gray-500">Administrator</span>
            </div>

            <!-- Main Navigation -->
            <div class="space-y-1">
                <!-- Dashboard -->
                <a href="home.php"
                    class="flex items-center p-2.5 text-gray-700 rounded-lg hover:bg-blue-50 group hover-transition">
                    <div
                        class="flex-shrink-0 w-10 h-10 flex items-center justify-center rounded-lg bg-blue-50 text-blue-600 group-hover:bg-blue-100">
                        <i class="fas fa-home"></i>
                    </div>
                    <div class="ml-3">
                        <p class="text-sm font-medium">Dashboard</p>
                        <p class="text-xs text-gray-500">Overview & stats</p>
                    </div>
                </a>

                <!-- Transactions Section -->
                <div class="mt-6">
                    <h3 class="px-2 text-xs font-semibold text-gray-500 uppercase tracking-wider">Transactions</h3>
                    <!-- Transaction Links -->
                    <div class="mt-2 space-y-1">
                        <!-- Monitoring -->
                        <a href="transaction-monitoring.php"
                            class="flex items-center p-2.5 text-gray-700 rounded-lg hover:bg-blue-50 group hover-transition">
                            <div
                                class="flex-shrink-0 w-10 h-10 flex items-center justify-center rounded-lg bg-gray-100 text-gray-600 group-hover:bg-gray-200">
                                <i class="fas fa-desktop"></i>
                            </div>
                            <div class="ml-3 flex-1">
                                <div class="flex justify-between items-center">
                                    <p class="text-sm font-medium">Monitoring</p>
                                    <?php if (($notif['Waiting'] ?? 0) > 0): ?>
                                        <span
                                            class="inline-flex items-center justify-center w-5 h-5 text-xs font-semibold text-white bg-gray-500 rounded-full"><?= $notif['Waiting'] ?? 0 ?></span>
                                    <?php endif; ?>
                                </div>
                                <p class="text-xs text-gray-500">Live tracking</p>
                            </div>
                        </a>

                        <!-- Pending -->
                        <a href="transaction-pending.php"
                            class="flex items-center p-2.5 text-gray-700 rounded-lg hover:bg-blue-50 group hover-transition">
                            <div
                                class="flex-shrink-0 w-10 h-10 flex items-center justify-center rounded-lg bg-yellow-100 text-yellow-600 group-hover:bg-yellow-200">
                                <i class="fas fa-clock"></i>
                            </div>
                            <div class="ml-3 flex-1">
                                <div class="flex justify-between items-center">
                                    <p class="text-sm font-medium">Pending</p>
                                    <?php if (($notif['Pending'] ?? 0) > 0): ?>
                                        <span
                                            class="inline-flex items-center justify-center w-5 h-5 text-xs font-semibold text-white bg-yellow-500 rounded-full"><?= $notif['Pending'] ?? 0 ?></span>
                                    <?php endif; ?>
                                </div>
                                <p class="text-xs text-gray-500">Awaiting approval</p>
                            </div>
                        </a>

                        <!-- Approved -->
                        <a href="transaction-approved.php"
                            class="flex items-center p-2.5 text-gray-700 rounded-lg hover:bg-blue-50 group hover-transition">
                            <div
                                class="flex-shrink-0 w-10 h-10 flex items-center justify-center rounded-lg bg-green-100 text-green-600 group-hover:bg-green-200">
                                <i class="fas fa-check-circle"></i>
                            </div>
                            <div class="ml-3 flex-1">
                                <div class="flex justify-between items-center">
                                    <p class="text-sm font-medium">Approved</p>
                                    <?php if (($notif['Approved'] ?? 0) > 0): ?>
                                        <span
                                            class="inline-flex items-center justify-center w-5 h-5 text-xs font-semibold text-white bg-green-500 rounded-full"><?= $notif['Approved'] ?? 0 ?></span>
                                    <?php endif; ?>
                                </div>
                                <p class="text-xs text-gray-500">Ready for departure</p>
                            </div>
                        </a>
                        <!-- Declined -->
                        <a href="transaction-declined.php"
                            class="flex items-center p-2.5 text-gray-700 rounded-lg hover:bg-red-50 group hover-transition">
                            <div
                                class="flex-shrink-0 w-10 h-10 flex items-center justify-center rounded-lg bg-red-100 text-red-600 group-hover:bg-red-200">
                                <i class="fas fa-times-circle"></i>
                            </div>
                            <div class="ml-3 flex-1">
                                <div class="flex justify-between items-center">
                                    <p class="text-sm font-medium">Declined</p>
                                    <?php if (($notif['declined'] ?? 0) > 0): ?>
                                        <span
                                            class="inline-flex items-center justify-center w-5 h-5 text-xs font-semibold text-white bg-red-500 rounded-full"><?= $notif['declined'] ?? 0 ?></span>
                                    <?php endif; ?>
                                </div>
                                <p class="text-xs text-gray-500">Rejected bookings</p>
                            </div>
                        </a>

                        <!-- Canceled -->
                        <a href="transaction-canceled.php"
                            class="flex items-center p-2.5 text-gray-700 rounded-lg hover:bg-blue-50 group hover-transition">
                            <div
                                class="flex-shrink-0 w-10 h-10 flex items-center justify-center rounded-lg bg-blue-100 text-blue-600 group-hover:bg-blue-200">
                                <i class="fas fa-ban"></i>
                            </div>
                            <div class="ml-3 flex-1">
                                <div class="flex justify-between items-center">
                                    <p class="text-sm font-medium">Canceled</p>
                                </div>
                                <p class="text-xs text-gray-500">Terminated Booking</p>
                            </div>
                        </a>

                        <!-- Departed -->
                        <a href="transaction-departed.php"
                            class="flex items-center p-2.5 text-gray-700 rounded-lg hover:bg-blue-50 group hover-transition">
                            <div
                                class="flex-shrink-0 w-10 h-10 flex items-center justify-center rounded-lg bg-purple-100 text-purple-600 group-hover:bg-purple-200">
                                <i class="fas fa-ship"></i>
                            </div>
                            <div class="ml-3">
                                <p class="text-sm font-medium">Departed</p>
                                <p class="text-xs text-gray-500">Completed trips</p>
                            </div>
                        </a>

                        <!-- Vouchers -->
                        <a href="voucher.php"
                            class="flex items-center p-2.5 text-gray-700 rounded-lg hover:bg-blue-50 group hover-transition">
                            <div
                                class="flex-shrink-0 w-10 h-10 flex items-center justify-center rounded-lg bg-blue-100 text-blue-600 group-hover:bg-blue-200">
                                <i class="fas fa-ticket-alt"></i>
                            </div>
                            <div class="ml-3">
                                <p class="text-sm font-medium">Vouchers</p>
                                <p class="text-xs text-gray-500">Manage vouchers</p>
                            </div>
                        </a>
                    </div>
                </div>

                <!-- Management Section -->
                <div class="mt-6">
                    <h3 class="px-2 text-xs font-semibold text-gray-500 uppercase tracking-wider">Management</h3>
                    <div class="mt-2 space-y-1">
                        <!-- User Accounts Dropdown -->
                        <div class="<?= $hideDiv; ?>">
                            <button type="button"
                                class="flex items-center w-full p-2.5 text-gray-700 rounded-lg hover:bg-blue-50 group hover-transition"
                                aria-controls="account-dropdown" data-collapse-toggle="account-dropdown">
                                <div
                                    class="flex-shrink-0 w-10 h-10 flex items-center justify-center rounded-lg bg-indigo-100 text-indigo-600 group-hover:bg-indigo-200">
                                    <i class="fas fa-users"></i>
                                </div>
                                <div class="ml-3 flex-1">
                                    <div class="flex justify-between items-center">
                                        <p class="text-sm font-medium">User Accounts</p>
                                        <i class="fas fa-chevron-down text-xs text-gray-400"></i>
                                    </div>
                                    <p class="text-xs text-gray-500">Manage users</p>
                                </div>
                            </button>
                            <div id="account-dropdown" class="hidden py-2 space-y-1 pl-14">
                                <a href="personnel.php"
                                    class="flex items-center p-2 text-gray-700 rounded-lg hover:bg-blue-50 group">
                                    <i class="fas fa-user-tie w-5 h-5 text-gray-500 mr-2"></i>
                                    <span>Personnel</span>
                                </a>
                                <a href="operator.php"
                                    class="flex items-center p-2 text-gray-700 rounded-lg hover:bg-blue-50 group">
                                    <i class="fas fa-user-cog w-5 h-5 text-gray-500 mr-2"></i>
                                    <span>Operators</span>
                                </a>
                            </div>
                        </div>

                        <!-- Port Lists -->
                        <a href="port.php"
                            class="flex items-center p-2.5 text-gray-700 rounded-lg hover:bg-blue-50 group hover-transition <?= $hideDiv; ?>">
                            <div
                                class="flex-shrink-0 w-10 h-10 flex items-center justify-center rounded-lg bg-teal-100 text-teal-600 group-hover:bg-teal-200">
                                <i class="fas fa-list-ul"></i>
                            </div>
                            <div class="ml-3">
                                <p class="text-sm font-medium">Port Lists</p>
                                <p class="text-xs text-gray-500">Manage ports</p>
                            </div>
                        </a>

                        <!-- Summary Dropdown -->
                        <div>
                            <button type="button"
                                class="flex items-center w-full p-2.5 text-gray-700 rounded-lg hover:bg-blue-50 group hover-transition"
                                aria-controls="summary-dropdown" data-collapse-toggle="summary-dropdown">
                                <div
                                    class="flex-shrink-0 w-10 h-10 flex items-center justify-center rounded-lg bg-orange-100 text-orange-600 group-hover:bg-orange-200">
                                    <i class="fas fa-chart-bar"></i>
                                </div>
                                <div class="ml-3 flex-1">
                                    <div class="flex justify-between items-center">
                                        <p class="text-sm font-medium">Summary</p>
                                        <i class="fas fa-chevron-down text-xs text-gray-400"></i>
                                    </div>
                                    <p class="text-xs text-gray-500">Reports & analytics</p>
                                </div>
                            </button>
                            <div id="summary-dropdown" class="hidden py-2 space-y-1 pl-14">
                                <a href="transaction.php"
                                    class="flex items-center p-2 text-gray-700 rounded-lg hover:bg-blue-50 group">
                                    <i class="fas fa-exchange-alt w-5 h-5 text-gray-500 mr-2"></i>
                                    <span>Transaction</span>
                                </a>
                                <a href="demographic.php"
                                    class="flex items-center p-2 text-gray-700 rounded-lg hover:bg-blue-50 group">
                                    <i class="fas fa-users w-5 h-5 text-gray-500 mr-2"></i>
                                    <span>Demographic</span>
                                </a>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Maintenance Section -->
                <div class="mt-6 <?= $hideDiv; ?>">
                    <h3 class="px-2 text-xs font-semibold text-gray-500 uppercase tracking-wider">Maintenance</h3>
                    <div class="mt-2 space-y-1">
                        <!-- Logs -->
                        <a href="logs.php"
                            class="flex items-center p-2.5 text-gray-700 rounded-lg hover:bg-blue-50 group hover-transition">
                            <div
                                class="flex-shrink-0 w-10 h-10 flex items-center justify-center rounded-lg bg-red-100 text-red-600 group-hover:bg-red-200">
                                <i class="fas fa-clipboard-list"></i>
                            </div>
                            <div class="ml-3">
                                <p class="text-sm font-medium">Logs</p>
                                <p class="text-xs text-gray-500">System activity</p>
                            </div>
                        </a>

                        <!-- System Maintenance -->
                        <a href="system-maintenance.php"
                            class="flex items-center p-2.5 text-gray-700 rounded-lg hover:bg-blue-50 group hover-transition">
                            <div
                                class="flex-shrink-0 w-10 h-10 flex items-center justify-center rounded-lg bg-gray-100 text-gray-600 group-hover:bg-gray-200">
                                <i class="fas fa-tools"></i>
                            </div>
                            <div class="ml-3">
                                <p class="text-sm font-medium">System Maintenance</p>
                                <p class="text-xs text-gray-500">Configuration</p>
                            </div>
                        </a>
                    </div>
                </div>
            </div>
    </aside>