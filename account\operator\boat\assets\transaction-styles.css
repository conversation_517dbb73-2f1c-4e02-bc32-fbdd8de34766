/* Custom Card Styles */
.booking-card {
    transition: all 0.3s ease;
    border: 1px solid #e5e7eb;
    background: linear-gradient(135deg, #ffffff 0%, #f9fafb 100%);
}

.booking-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
    border-color: #d1d5db;
}

.action-button {
    transition: all 0.2s ease;
    padding: 0.5rem 1rem;
    border-radius: 0.5rem;
    font-size: 0.875rem;
    font-weight: 500;
    min-width: 80px;
    border: none;
    cursor: pointer;
    display: inline-flex;
    align-items: center;
    justify-content: center;
}

.action-button:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.status-badge {
    display: inline-flex;
    align-items: center;
    padding: 0.25rem 0.75rem;
    border-radius: 9999px;
    font-size: 0.75rem;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

/* Pending Status Styles */
.status-pending {
    background-color: #fef3c7;
    color: #d97706;
    border: 1px solid #fbbf24;
}

.status-ready {
    background-color: #dcfce7;
    color: #16a34a;
    padding: 0.25rem 0.75rem;
    border-radius: 9999px;
    font-size: 0.75rem;
    font-weight: 500;
}

.status-canceled {
    background-color: #fee2e2;
    color: #dc2626;
    border: 1px solid #f87171;
}

.status-declined {
    background-color: #fef2f2;
    color: #dc2626;
    border: 1px solid #fecaca;
}

.btn-close.pending {
    background: linear-gradient(135deg, #6b7280 0%, #4b5563 100%);
    color: white;
}

.btn-close.pending:hover {
    background: linear-gradient(135deg, #4b5563 0%, #374151 100%);
}

.btn-submit.pending {
    background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
    color: white;
}

.btn-submit.pending:hover {
    background: linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%);
}

/* Approved Status Styles */
.status-approved {
    background-color: #dcfce7;
    color: #16a34a;
    border: 1px solid #22c55e;
}

.status-ready {
    background-color: #d1fae5;
    color: #059669;
    border: 1px solid #34d399;
}

.btn-close.approved {
    background: linear-gradient(135deg, #10b981 0%, #059669 100%);
    color: white;
}

.btn-close.approved:hover {
    background: linear-gradient(135deg, #059669 0%, #047857 100%);
}

.btn-submit.approved {
    background: linear-gradient(135deg, #10b981 0%, #059669 100%);
    color: white;
}

.btn-submit.approved:hover {
    background: linear-gradient(135deg, #059669 0%, #047857 100%);
}

/* Button Color Styles */
.btn-download {
    background: linear-gradient(135deg, #10b981 0%, #059669 100%);
    color: white;
}

.btn-download:hover {
    background: linear-gradient(135deg, #059669 0%, #047857 100%);
}

.btn-view {
    background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
    color: white;
}

.btn-view:hover {
    background: linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%);
}

.btn-payment {
    background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
    color: white;
}

.btn-payment:hover {
    background: linear-gradient(135deg, #d97706 0%, #b45309 100%);
}

.btn-cancel {
    background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
    color: white;
}

.btn-cancel:hover {
    background: linear-gradient(135deg, #dc2626 0%, #b91c1c 100%);
}

.btn-cancel:disabled {
    background: linear-gradient(135deg, #9ca3af 0%, #6b7280 100%);
    cursor: not-allowed;
    opacity: 0.6;
}