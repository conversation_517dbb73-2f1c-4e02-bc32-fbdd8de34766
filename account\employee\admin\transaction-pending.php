<?php
require '_header.php';
?>

<link rel="stylesheet" href="assets/transaction-styles.css">

<div class="p-4 sm:ml-64">
    <div class="mt-16 fade-in"> <!-- Adjusted for top navbar -->
        <!-- Header Section -->
        <div class="bg-gradient-to-r from-blue-600 to-blue-700 rounded-xl shadow-lg p-6 mb-8">
            <!-- Breadcrumb -->
            <nav class="flex mb-4" aria-label="Breadcrumb">
                <ol class="inline-flex items-center space-x-1 md:space-x-2">
                    <li class="inline-flex items-center">
                        <a href="home.php"
                            class="inline-flex items-center text-sm font-medium text-blue-100 hover:text-white transition-colors">
                            <svg class="w-4 h-4 me-2" fill="currentColor" viewBox="0 0 20 20">
                                <path
                                    d="m19.707 9.293-2-2-7-7a1 1 0 0 0-1.414 0l-7 7-2 2a1 1 0 0 0 1.414 1.414L2 10.414V18a2 2 0 0 0 2 2h3a1 1 0 0 0 1-1v-4a1 1 0 0 1 1-1h2a1 1 0 0 1 1 1v4a1 1 0 0 0 1 1h3a2 2 0 0 0 2-2v-7.586l.293.293a1 1 0 0 0 1.414-1.414Z" />
                            </svg>
                            Home
                        </a>
                    </li>
                    <li>
                        <div class="flex items-center">
                            <svg class="w-3 h-3 text-blue-200 mx-1" fill="none" viewBox="0 0 6 10">
                                <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round"
                                    stroke-width="2" d="m1 9 4-4-4-4" />
                            </svg>
                            <span class="text-sm font-medium text-blue-100">Transactions</span>
                        </div>
                    </li>
                    <li>
                        <div class="flex items-center">
                            <svg class="w-3 h-3 text-blue-200 mx-1" fill="none" viewBox="0 0 6 10">
                                <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round"
                                    stroke-width="2" d="m1 9 4-4-4-4" />
                            </svg>
                            <span class="text-sm font-medium text-white">Pending</span>
                        </div>
                    </li>
                </ol>
            </nav>

            <!-- Title Section -->
            <div class="flex items-center">
                <div class="bg-white bg-opacity-20 p-3 rounded-xl mr-4">
                    <i class="fas fa-clock text-white text-2xl"></i>
                </div>
                <div>
                    <h1 class="text-3xl font-bold text-white mb-1">Pending Bookings</h1>
                    <p class="text-blue-100">Bookings awaiting approval from operators</p>
                </div>
            </div>
        </div>

        <!-- Search and Filter Section -->
        <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-4 mb-6">
            <div class="flex flex-col sm:flex-row gap-4 items-center justify-between">
                <div class="relative flex-1 max-w-md">
                    <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                        <i class="fas fa-search text-gray-400"></i>
                    </div>
                    <input type="text" id="searchInput" placeholder="Search bookings..."
                        class="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors">
                </div>
                <div class="flex items-center gap-2 text-sm text-gray-600">
                    <i class="fas fa-info-circle"></i>
                    <span id="bookingCount">Loading bookings...</span>
                </div>
            </div>
        </div>

        <!-- Bookings Grid -->
        <div id="bookingsContainer" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            <?php
            try {
                $getMthoStatus = "Pending";
                $getBookingStatus = "pending";

                $bookingDetails = getBookingDetails($pdo, $getMthoStatus, $getBookingStatus);

                if ($bookingDetails && count($bookingDetails) > 0) {
                    foreach ($bookingDetails as $row) {
                        $booking_id = $row['booking_id'];
                        $operatorName = trim($row['firstname'] . ' ' . $row['middlename'] . ' ' . $row['lastname'] . ' ' . $row['extname']);
                        $designationTour = $row['tour_operator_designation'];
                        $destination = $row['resort_operator_designation'];
                        $referenceNumber = $row['referenceNum'];

                        // Check if the booking is canceled
                        $isCanceled = isset($row['cancellation_booking_status']) && $row['cancellation_booking_status'] === 'request';

                        // Check if both resort and boat are approved
                        if (!$isCanceled && isset($row['mtho']) && $row['mtho'] === "Waiting") {
                            $bothApproved = (isset($row['resort']) && $row['resort'] === "Approved" && isset($row['boat']) && $row['boat'] === "Approved");
                        } else {
                            $bothApproved = false;
                        }

                        if (isset($row['payment_status']) && $row['payment_status'] === 'voucher') {
                            $showBadge = '<span class="badge inline-flex items-center bg-purple-100 text-purple-800 border border-purple-300 rounded-full px-3 py-1 text-xs font-semibold">
                            <i class="fas fa-ticket-alt mr-1 text-purple-500"></i>
                            Voucher
                            </span>';
                        } else {
                            $showBadge = '';
                        }
                        ?>
                        <!-- Booking Card -->
                        <div class="booking-card rounded-lg p-6 fade-in"
                            data-reference="<?= htmlspecialchars($referenceNumber ?? ''); ?>"
                            data-operator="<?= htmlspecialchars($operatorName ?? ''); ?>"
                            data-designation="<?= htmlspecialchars($designationTour ?? ''); ?>"
                            data-destination="<?= htmlspecialchars($destination ?? ''); ?>"
                            data-status="<?= $isCanceled ? 'canceled' : ($bothApproved ? 'ready' : 'pending'); ?>">
                            <!-- Card Header -->
                            <div class="flex justify-between items-start mb-4">
                                <div class="flex-1">
                                    <h3 class="text-lg font-semibold text-gray-900 mb-2">
                                        <?= htmlspecialchars($referenceNumber ?? 'N/A'); ?>
                                        <?= $showBadge; ?>
                                    </h3>
                                    <div
                                        class="status-badge <?= $isCanceled ? 'status-canceled' : ($bothApproved ? 'status-ready' : 'status-pending'); ?>">
                                        <?php if ($isCanceled): ?>
                                            <i class="fas fa-ban mr-1"></i>
                                            Requesting Cancelation
                                        <?php elseif ($bothApproved): ?>
                                            <i class="fas fa-check-circle mr-1"></i>
                                            Ready for Payment
                                        <?php else: ?>
                                            <i class="fas fa-clock mr-1"></i>
                                            Awaiting Approval
                                        <?php endif; ?>
                                    </div>
                                </div>
                            </div>

                            <!-- Booking Details -->
                            <div class="space-y-3 mb-4">
                                <div class="flex items-center text-sm">
                                    <i class="fas fa-user text-gray-400 w-4 mr-3"></i>
                                    <span class="text-gray-600">Operator:</span>
                                    <span
                                        class="ml-2 font-medium text-gray-900"><?= htmlspecialchars($operatorName ?? 'N/A'); ?></span>
                                </div>
                                <div class="flex items-center text-sm">
                                    <i class="fas fa-briefcase text-gray-400 w-4 mr-3"></i>
                                    <span class="text-gray-600">Designation:</span>
                                    <span
                                        class="ml-2 font-medium text-gray-900"><?= htmlspecialchars($designationTour ?? 'N/A'); ?></span>
                                </div>
                                <div class="flex items-center text-sm">
                                    <i class="fas fa-map-marker-alt text-gray-400 w-4 mr-3"></i>
                                    <span class="text-gray-600">Destination:</span>
                                    <span
                                        class="ml-2 font-medium text-gray-900"><?= htmlspecialchars($destination ?? 'N/A'); ?></span>
                                </div>
                                <?php if (isset($row['check_in_date']) && isset($row['check_out_date'])): ?>
                                    <div class="flex items-center text-sm">
                                        <i class="fas fa-calendar text-gray-400 w-4 mr-3"></i>
                                        <span class="text-gray-600">Date:</span>
                                        <span class="ml-2 font-medium text-gray-900"><?= htmlspecialchars($row['check_in_date']); ?> -
                                            <?= htmlspecialchars($row['check_out_date']); ?>
                                        </span>
                                    </div>
                                <?php endif; ?>
                                <?php if (isset($row['total_adults']) && isset($row['total_children'])): ?>
                                    <div class="flex items-center text-sm">
                                        <i class="fas fa-users text-gray-400 w-4 mr-3"></i>
                                        <span class="text-gray-600">Total Pax:</span>
                                        <span
                                            class="ml-2 font-medium text-gray-900"><?= htmlspecialchars($row['total_adults'] + $row['total_children']); ?>
                                        </span>
                                    </div>
                                <?php endif; ?>
                            </div>

                            <!-- Action Buttons -->
                            <div class="flex gap-2">
                                <a href="view-pending-transaction.php?id=<?= $booking_id; ?>"
                                    class="flex-1 inline-flex items-center justify-center px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white text-sm font-medium rounded-lg transition-colors duration-200">
                                    <i class="fas fa-eye mr-1.5"></i>
                                    View Details
                                </a>
                            </div>
                        </div>
                        <?php
                    }
                } else {
                    ?>
                    <div class="col-span-full">
                        <div class="booking-card rounded-lg p-8 text-center">
                            <div class="flex flex-col items-center justify-center">
                                <div class="bg-blue-100 p-4 rounded-full mb-4">
                                    <i class="fas fa-clock text-blue-500 text-2xl"></i>
                                </div>
                                <h3 class="text-lg font-semibold text-gray-900 mb-2">No Pending Bookings</h3>
                                <p class="text-gray-500 text-sm mb-4 max-w-sm">Pending bookings will appear here when they are
                                    awaiting approval.</p>
                                <a href="home.php" class="action-button btn-view">
                                    <i class="fas fa-arrow-left mr-2"></i>
                                    Back to Dashboard
                                </a>
                            </div>
                        </div>
                    </div>
                    <?php
                }
            } catch (PDOException $e) {
                ?>
                <div class="col-span-full">
                    <div class="booking-card rounded-lg p-8 text-center border-red-200 bg-red-50">
                        <div class="flex flex-col items-center justify-center">
                            <div class="bg-red-100 p-4 rounded-full mb-4">
                                <i class="fas fa-exclamation-triangle text-red-500 text-2xl"></i>
                            </div>
                            <h3 class="text-lg font-semibold text-red-900 mb-2">Error Loading Bookings</h3>
                            <p class="text-red-600 text-sm mb-4"><?= htmlspecialchars($e->getMessage()); ?></p>
                            <button onclick="location.reload()" class="action-button btn-view">
                                <i class="fas fa-refresh mr-2"></i>
                                Try Again
                            </button>
                        </div>
                    </div>
                </div>
                <?php
            }
            ?>
        </div>
    </div>

    <?php
    require '_footer.php';
    ?>