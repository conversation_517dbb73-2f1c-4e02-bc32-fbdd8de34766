<?php
session_start([
    'cookie_secure'   => true,       // Only send cookie over HTTPS
    'cookie_httponly' => true,       // <PERSON><PERSON> cannot access the cookie
    'cookie_samesite' => 'Strict'    // Prevent CSRF in modern browsers
]);

include '../../../connection/dbconnect.php';
require_once 'inc/inc.function.php';

if (empty($_SESSION['accountType']) || empty($_SESSION['id'])) {
    header("Location: ../../../login.php");
    exit();
}

// Validate that ID is a positive integer
$id = isset($_SESSION['id']) ? (int) $_SESSION['id'] : 0;
if ($id <= 0) {
    // Invalid user ID; force re-login
    header("Location: ../../../login.php");
    exit();
}

// Retrieve account type from session
$accType = $_SESSION['accountType'];

// Generate a new CSRF token if needed (none or expired)
if (empty($_SESSION['csrf_token']) || time() > ($_SESSION['csrf_token_expiration'] ?? 0)) {
    $_SESSION['csrf_token'] = bin2hex(random_bytes(32));
    // 1-hour validity from now
    $_SESSION['csrf_token_expiration'] = time() + 3600;
}

$sql_global = '
    SELECT
        i.firstname,
        i.middlename,
        i.lastname,
        i.extname,
        a.accType,
        a.email,
        a.username,
        a.login_status
    FROM operator_info i
    INNER JOIN operator_account a ON i.user_id = a.id
    WHERE i.user_id = :id
';

$stmt_global = $pdo->prepare($sql_global);
$stmt_global->bindParam(':id', $id, PDO::PARAM_INT);

try {
    $stmt_global->execute();
} catch (PDOException $e) {
    error_log("Error executing combined query: " . $e->getMessage());
    die("An error occurred. Please try again later.");
}

$row_global = $stmt_global->fetch(PDO::FETCH_ASSOC);
if (!$row_global) {
    header("Location: ../../../login.php");
    exit();
}
if ($row_global['accType'] !== $accType) {
    header("Location: ../../../login.php");
    exit();
}

if ($row_global['login_status'] != "isLogin") {
    header("Location: logout.php");
    exit;
}

// Construct user's full name
$name = trim(
    $row_global['firstname'] . ' ' .
        $row_global['middlename'] . ' ' .
        $row_global['lastname']   . ' ' .
        $row_global['extname']
);
$email = $row_global['email'];

// Escape the name for HTML output
$nameEscaped = htmlspecialchars($name, ENT_QUOTES, 'UTF-8');

// Retrieve notifications for the user
$notif = getNotification($pdo, $id);

// Get current page name for active sidebar highlighting
$currentPage = basename($_SERVER['PHP_SELF']);
?>

<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <link rel="icon" type="image/x-icon" href="../../../components/img/Logo.png" />
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
    <link href="https://cdn.jsdelivr.net/npm/flowbite@2.5.2/dist/flowbite.min.css" rel="stylesheet" />
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/all.min.css" />
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <title>Boat Operator | Calaguas TDF Portal</title>

    <style>
        .hover-transition {
            transition: all 0.3s ease;
        }

        .sidebar-active {
            background-color: #EFF6FF; /* bg-blue-50 */
            color: #2563EB; /* text-blue-600 */
        }

        .sidebar-active .icon-container {
            background-color: #DBEAFE; /* bg-blue-100 */
        }
    </style>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Get current page filename
            const path = window.location.pathname;
            const filename = path.substring(path.lastIndexOf('/') + 1);
            const pageName = filename.split('.')[0]; // Remove extension

            // Find the matching sidebar link and add active class
            document.querySelectorAll('#logo-sidebar a').forEach(link => {
                const href = link.getAttribute('href');
                if (href === filename || (pageName === 'home' && href === 'home.php')) {
                    link.classList.add('sidebar-active');
                } else if (pageName.includes('booking') && href === 'booking.php') {
                    link.classList.add('sidebar-active');
                } else if (pageName.includes('transaction-draft') && href === 'transaction-draft.php') {
                    link.classList.add('sidebar-active');
                } else if (pageName.includes('transaction-pending') && href === 'transaction-pending.php') {
                    link.classList.add('sidebar-active');
                } else if (pageName.includes('transaction-declined') && href === 'transaction-declined.php') {
                    link.classList.add('sidebar-active');
                } else if (pageName.includes('transaction-approved') && href === 'transaction-approved.php') {
                    link.classList.add('sidebar-active');
                } else if (pageName.includes('transaction-archive') && href === 'transaction-archive.php') {
                    link.classList.add('sidebar-active');
                }
            });
        });
    </script>
</head>

<body>
    <nav class="fixed top-0 z-50 w-full bg-gradient-to-r from-blue-600 to-blue-800 border-b border-blue-700 shadow-md">
        <div class="px-3 py-3 lg:px-5 lg:pl-3">
            <div class="flex items-center justify-between">
                <div class="flex items-center justify-start rtl:justify-end">
                    <button data-drawer-target="logo-sidebar" data-drawer-toggle="logo-sidebar" aria-controls="logo-sidebar" type="button" class="inline-flex items-center p-2 text-sm text-white rounded-lg sm:hidden hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500">
                        <span class="sr-only">Open sidebar</span>
                        <svg class="w-6 h-6" aria-hidden="true" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                            <path clip-rule="evenodd" fill-rule="evenodd" d="M2 4.75A.75.75 0 012.75 4h14.5a.75.75 0 010 1.5H2.75A.75.75 0 012 4.75zm0 10.5a.75.75 0 01.75-.75h7.5a.75.75 0 010 1.5h-7.5a.75.75 0 01-.75-.75zM2 10a.75.75 0 01.75-.75h14.5a.75.75 0 010 1.5H2.75A.75.75 0 012 10z"></path>
                        </svg>
                    </button>
                    <a href="home.php" class="flex ms-2 md:me-24">
                        <img src="../../../components/img/Logo.png" class="h-8 me-3 bg-white rounded-full p-1" alt="Calaguas Logo" />
                        <div class="flex flex-col">
                            <span class="self-center text-xl font-semibold text-white">Calaguas TDF</span>
                            <span class="text-xs text-blue-200">Boat Operator Portal</span>
                        </div>
                    </a>
                </div>
                <div class="flex items-center gap-4">
                    <!-- Notifications -->
                    <div class="relative">

                        <button type="button" class="relative p-2 text-white bg-blue-700 rounded-full hover:bg-blue-800 focus:ring-2 focus:ring-blue-300" aria-expanded="false" data-dropdown-toggle="notification-dropdown">
                            <span class="sr-only">View notifications</span>
                            <i class="fas fa-bell"></i>
                            <?php if (array_sum($notif) > 0): ?>
                            <div class="absolute inline-flex items-center justify-center w-5 h-5 text-xs font-bold text-white bg-red-500 border-2 border-white rounded-full -top-1 -right-1"><?= array_sum($notif) ?></div>
                            <?php endif; ?>
                        </button>
                        <!-- Dropdown menu -->
                        <div class="hidden z-50 my-4 w-80 text-base list-none bg-white divide-y divide-gray-100 rounded-lg shadow-lg" id="notification-dropdown">
                            <div class="block px-4 py-2 font-medium text-gray-700 bg-gray-50 rounded-t-lg">
                                Notifications
                            </div>
                            <div class="divide-y divide-gray-100 max-h-96 overflow-y-auto">
                                <?php if (array_sum($notif) === 0): ?>
                                <div class="flex px-4 py-3 hover:bg-gray-50">
                                    <div class="w-full pl-3">
                                        <p class="text-sm text-gray-500">No new notifications</p>
                                    </div>
                                </div>
                                <?php else: ?>
                                <?php if (($notif['pending'] ?? 0) > 0): ?>
                                <a href="transaction-pending.php" class="flex px-4 py-3 hover:bg-gray-50">
                                    <div class="flex-shrink-0">
                                        <div class="w-10 h-10 rounded-full bg-yellow-100 flex items-center justify-center text-yellow-500">
                                            <i class="fas fa-clock"></i>
                                        </div>
                                    </div>
                                    <div class="w-full pl-3">
                                        <p class="text-sm font-medium text-gray-900">Pending Bookings</p>
                                        <p class="text-xs text-gray-500">You have <?= $notif['pending'] ?> pending booking(s) to review</p>
                                    </div>
                                </a>
                                <?php endif; ?>
                                <?php endif; ?>
                            </div>
                            <a href="#" class="block py-2 text-sm font-medium text-center text-gray-900 bg-gray-50 hover:bg-gray-100 rounded-b-lg">
                                View all notifications
                            </a>
                        </div>
                    </div>

                    <!-- User menu -->
                    <div class="relative">
                        <button type="button" class="flex items-center gap-2 text-sm text-white rounded-full focus:ring-2 focus:ring-blue-300" aria-expanded="false" data-dropdown-toggle="dropdown-user">
                            <span class="sr-only">Open user menu</span>
                            <div class="w-8 h-8 rounded-full bg-blue-100 flex items-center justify-center text-blue-600">
                                <i class="fas fa-user"></i>
                            </div>
                            <span class="hidden md:inline-block"><?= explode(' ', $nameEscaped)[0]; ?></span>
                            <i class="fas fa-chevron-down text-xs"></i>
                        </button>
                        <!-- Dropdown menu -->
                        <div class="hidden z-50 my-4 w-56 text-base list-none bg-white divide-y divide-gray-100 rounded-lg shadow-lg" id="dropdown-user">
                            <div class="px-4 py-3">
                                <p class="text-sm font-medium text-gray-900"><?= $nameEscaped ?></p>
                                <p class="text-sm text-gray-500 truncate"><?= htmlspecialchars($email, ENT_QUOTES, 'UTF-8') ?></p>
                            </div>
                            <ul class="py-2">
                                <li>
                                    <a href="myaccount.php" class="flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                                        <i class="fas fa-user-cog w-5 text-gray-500"></i>
                                        <span class="ml-2">My Account</span>
                                    </a>
                                </li>
                                <li>
                                    <a href="logout.php" class="flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                                        <i class="fas fa-sign-out-alt w-5 text-gray-500"></i>
                                        <span class="ml-2">Sign out</span>
                                    </a>
                                </li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </nav>

    <aside id="logo-sidebar" class="fixed top-0 left-0 z-40 w-64 h-screen pt-20 transition-transform -translate-x-full bg-white border-r border-gray-200 sm:translate-x-0 shadow-md" aria-label="Sidebar">
        <div class="h-full px-3 pb-4 overflow-y-auto bg-white">
            <!-- User Profile Summary -->
            <div class="flex flex-col items-center pb-5 mb-5 border-b border-gray-200">
                <div class="w-20 h-20 mb-3 rounded-full bg-blue-100 flex items-center justify-center text-blue-600">
                    <i class="fas fa-ship text-3xl"></i>
                </div>
                <h5 class="mb-1 text-lg font-medium text-gray-900"><?= explode(' ', $nameEscaped)[0]; ?></h5>
                <span class="text-sm text-gray-500">Boat Operator</span>
            </div>

            <!-- Main Navigation -->
            <div class="space-y-1">
                <!-- Dashboard -->
                <a href="home.php" class="flex items-center p-2.5 text-gray-700 rounded-lg hover:bg-blue-50 group hover-transition">
                    <div class="flex-shrink-0 w-10 h-10 flex items-center justify-center rounded-lg bg-blue-50 text-blue-600 group-hover:bg-blue-100">
                        <i class="fas fa-home"></i>
                    </div>
                    <div class="ml-3">
                        <p class="text-sm font-medium">Dashboard</p>
                        <p class="text-xs text-gray-500">Overview & stats</p>
                    </div>
                </a>

                <!-- Pending -->
                <a href="transaction-pending.php" class="flex items-center p-2.5 text-gray-700 rounded-lg hover:bg-yellow-50 group hover-transition">
                    <div class="flex-shrink-0 w-10 h-10 flex items-center justify-center rounded-lg bg-yellow-100 text-yellow-600 group-hover:bg-yellow-200">
                        <i class="fas fa-clock"></i>
                    </div>
                    <div class="ml-3 flex-1">
                        <div class="flex justify-between items-center">
                            <p class="text-sm font-medium">Pending</p>
                            <?php if (($notif['pending'] ?? 0) > 0): ?>
                            <span class="inline-flex items-center justify-center w-5 h-5 text-xs font-semibold text-white bg-yellow-500 rounded-full"><?= $notif['pending'] ?? 0 ?></span>
                            <?php endif; ?>
                        </div>
                        <p class="text-xs text-gray-500">Awaiting approval</p>
                    </div>
                </a>

                <!-- Approved -->
                <a href="transaction-approved.php" class="flex items-center p-2.5 text-gray-700 rounded-lg hover:bg-green-50 group hover-transition">
                    <div class="flex-shrink-0 w-10 h-10 flex items-center justify-center rounded-lg bg-green-100 text-green-600 group-hover:bg-green-200">
                        <i class="fas fa-check-circle"></i>
                    </div>
                    <div class="ml-3 flex-1">
                        <div class="flex justify-between items-center">
                            <p class="text-sm font-medium">Approved</p>
                            <?php if (($notif['approved'] ?? 0) > 0): ?>
                            <span class="inline-flex items-center justify-center w-5 h-5 text-xs font-semibold text-white bg-green-500 rounded-full"><?= $notif['approved'] ?? 0 ?></span>
                            <?php endif; ?>
                        </div>
                        <p class="text-xs text-gray-500">Confirmed bookings</p>
                    </div>
                </a>
                                <!-- Declined -->
                <a href="transaction-declined.php" class="flex items-center p-2.5 text-gray-700 rounded-lg hover:bg-red-50 group hover-transition">
                    <div class="flex-shrink-0 w-10 h-10 flex items-center justify-center rounded-lg bg-red-100 text-red-600 group-hover:bg-red-200">
                        <i class="fas fa-times-circle"></i>
                    </div>
                    <div class="ml-3 flex-1">
                        <div class="flex justify-between items-center">
                            <p class="text-sm font-medium">Declined</p>
                            <?php if (($notif['declined'] ?? 0) > 0): ?>
                            <span class="inline-flex items-center justify-center w-5 h-5 text-xs font-semibold text-white bg-red-500 rounded-full"><?= $notif['declined'] ?? 0 ?></span>
                            <?php endif; ?>
                        </div>
                        <p class="text-xs text-gray-500">Rejected bookings</p>
                    </div>
                </a>

                <!-- Canceled -->
                <a href="transaction-canceled.php" class="flex items-center p-2.5 text-gray-700 rounded-lg hover:bg-blue-50 group hover-transition">
                    <div class="flex-shrink-0 w-10 h-10 flex items-center justify-center rounded-lg bg-blue-100 text-blue-600 group-hover:bg-blue-200">
                        <i class="fas fa-ban"></i>
                    </div>
                    <div class="ml-3 flex-1">
                        <div class="flex justify-between items-center">
                            <p class="text-sm font-medium">Canceled</p>
                        </div>
                        <p class="text-xs text-gray-500">Terminated Booking</p>
                    </div>
                </a>
            </div>

            <!-- Settings Section (if needed) -->
            <div class="mt-6">
                <h2 class="text-sm font-semibold text-gray-500 uppercase">Settings</h2>
                <div class="mt-2 space-y-1">
                    <a href="myaccount.php" class="flex items-center p-2.5 text-gray-700 rounded-lg hover:bg-gray-50 group hover-transition">
                        <div class="flex-shrink-0 w-10 h-10 flex items-center justify-center rounded-lg bg-gray-100 text-gray-600 group-hover:bg-gray-200">
                            <i class="fas fa-user-cog"></i>
                        </div>
                        <div class="ml-3">
                            <p class="text-sm font-medium">My Account</p>
                            <p class="text-xs text-gray-500">Profile settings</p>
                        </div>
                    </a>
                </div>
            </div>
        </div>
    </aside>