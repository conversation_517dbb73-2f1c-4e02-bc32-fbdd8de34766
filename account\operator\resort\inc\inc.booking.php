<?php
session_start([
    'cookie_secure' => true,       // Only send cookie over HTTPS
    'cookie_httponly' => true,       // <PERSON><PERSON> cannot access the cookie
    'cookie_samesite' => 'Strict'    // Prevent CSRF in modern browsers
]);
include '../../../../connection/dbconnect.php';
if ($_SESSION['loginStatus'] != "isLogin") {
    header("Location: ../logout.php");
    exit;
} else {
    if ($_SERVER["REQUEST_METHOD"] == "POST") {
        if (isset($_POST['resortResponseBtn'])) {
            try {
                // CSRF check
                if (
                    !isset($_POST['csrf_token'], $_SESSION['csrf_token']) ||
                    $_POST['csrf_token'] !== $_SESSION['csrf_token']
                ) {
                    throw new Exception("Invalid CSRF token.");
                }
                if (!isset($_SESSION['id'])) {
                    throw new Exception("User not authenticated");
                }

                // Validate Booking ID
                $getBookingId = $_POST['booking_id'] ?? null;
                if (!$getBookingId) {
                    throw new Exception("Invalid booking ID.");
                }

                // Start transaction
                $pdo->beginTransaction();

                $resortResponse = trim($_POST['resortResponse'] ?? '');
                $referenceNumber = trim($_POST['referenceNumber'] ?? '');
                $newBoatStatus = "Pending";
                $updateBookingStatus = "declined";

                // UPDATE Approvals
                if ($resortResponse === "Approved") {
                    $stmt = $pdo->prepare(
                        "UPDATE cb_booking_approvals 
                         SET resort = :resortResponse, boat = :newBoatStatus 
                         WHERE booking_id = :booking_id"
                    );
                    $stmt->execute([
                        ':resortResponse' => $resortResponse,
                        ':newBoatStatus' => $newBoatStatus,
                        ':booking_id' => $getBookingId
                    ]);
                } else {
                    $stmt = $pdo->prepare(
                        "UPDATE cb_booking_approvals 
                         SET resort = :resortResponse,
                             date_updated = NOW()
                         WHERE booking_id = :booking_id"
                    );
                    $stmt->execute([
                        ':resortResponse' => $resortResponse,
                        ':booking_id' => $getBookingId
                    ]);

                    $stmt = $pdo->prepare(
                        "UPDATE cb_bookings 
                         SET booking_status = :updateBookingStatus 
                         WHERE booking_id = :booking_id"
                    );
                    $stmt->execute([
                        ':updateBookingStatus' => $updateBookingStatus,
                        ':booking_id' => $getBookingId
                    ]);

                    // Get booking details to check if vouchers need to be restored
                    $stmt = $pdo->prepare(
                        "SELECT * FROM cb_bookings WHERE booking_id = :booking_id"
                    );
                    $stmt->execute([':booking_id' => $getBookingId]);
                    $booking = $stmt->fetch(PDO::FETCH_ASSOC);

                    if (!$booking) {
                        throw new Exception('Booking not found.');
                    }

                    // Insert booking data into cb_booking_cancellation table with status 'declined'
                    $stmt = $pdo->prepare(
                        "INSERT INTO cb_booking_cancellation
                        (booking_id, referenceNum, tour_operator_id, resort_operator_id, boat_id, port_id,
                        check_in_date, check_out_date, booking_status, created_by:, date_created)
                        VALUES
                        (:booking_id, :referenceNum, :tour_operator_id, :resort_operator_id, :boat_id, :port_id,
                        :check_in_date, :check_out_date, 'declined', :created_by, NOW())"
                    );

                    $stmt->execute([
                        ':booking_id' => $booking['booking_id'],
                        ':referenceNum' => $booking['referenceNum'],
                        ':tour_operator_id' => $booking['tour_operator_id'],
                        ':resort_operator_id' => $booking['resort_operator_id'],
                        ':boat_id' => $booking['boat_id'],
                        ':port_id' => $booking['port_id'],
                        ':check_in_date' => $booking['check_in_date'],
                        ':check_out_date' => $booking['check_out_date'],
                        ':created_by' => $booking['resort_operator_id']
                    ]);
                }

                // Insert log entry
                $type = "Booking - " . $resortResponse;
                $description = "Resort operator: " . $_SESSION['username'] . " " . $resortResponse .
                    " a booking: " . $referenceNumber . ". Date Created: " . date("Y-m-d");
                $stmt = $pdo->prepare(
                    "INSERT INTO system_logs (type, description) VALUES (:type, :description)"
                );
                $stmt->execute([
                    ':type' => $type,
                    ':description' => $description
                ]);

                // Commit transaction
                $pdo->commit();

                $_SESSION['success'] = $resortResponse . " successfully";
                header("Location: ../transaction-approved.php");
                exit();
            } catch (Exception $e) {
                if ($pdo->inTransaction()) {
                    $pdo->rollBack();
                }

                // Enhanced error messages
                $errorMessage = $e->getMessage();
                if (strpos($errorMessage, 'CSRF') !== false) {
                    $_SESSION['error'] = 'Security token expired. Please refresh the page and try again.';
                } elseif (strpos($errorMessage, 'booking ID') !== false) {
                    $_SESSION['error'] = 'Invalid booking reference. Please contact support if this persists.';
                } elseif (strpos($errorMessage, 'authenticated') !== false) {
                    $_SESSION['error'] = 'Session expired. Please log in again.';
                } else {
                    $_SESSION['error'] = 'An unexpected error occurred: ' . $errorMessage;
                }

                header("Location: ../transaction-pending.php");
                exit();
            }
        }
    }
}
