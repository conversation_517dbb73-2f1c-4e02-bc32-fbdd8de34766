// Transaction Scripts for Treasurer

// Search functionality
document.addEventListener('DOMContentLoaded', function() {
    const searchInput = document.getElementById('searchInput');
    const bookingCards = document.querySelectorAll('.booking-card');
    const bookingCount = document.getElementById('bookingCount');

    // Update booking count
    function updateBookingCount() {
        const visibleCards = document.querySelectorAll('.booking-card:not([style*="display: none"])');
        const totalCards = bookingCards.length;
        bookingCount.textContent = `Showing ${visibleCards.length} of ${totalCards} bookings`;
    }

    // Initial count
    updateBookingCount();

    // Search functionality
    if (searchInput) {
        searchInput.addEventListener('input', function() {
            const searchTerm = this.value.toLowerCase().trim();
            
            bookingCards.forEach(card => {
                const reference = card.dataset.reference?.toLowerCase() || '';
                const operator = card.dataset.operator?.toLowerCase() || '';
                const status = card.dataset.status?.toLowerCase() || '';
                
                const isVisible = reference.includes(searchTerm) || 
                                operator.includes(searchTerm) || 
                                status.includes(searchTerm);
                
                card.style.display = isVisible ? 'block' : 'none';
            });
            
            updateBookingCount();
        });
    }

    // Add smooth animations
    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.style.opacity = '1';
                entry.target.style.transform = 'translateY(0)';
            }
        });
    }, {
        threshold: 0.1
    });

    bookingCards.forEach(card => {
        card.style.opacity = '0';
        card.style.transform = 'translateY(20px)';
        card.style.transition = 'opacity 0.5s ease, transform 0.5s ease';
        observer.observe(card);
    });

    // Enhanced button interactions
    document.querySelectorAll('.action-button').forEach(button => {
        button.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-2px)';
        });
        
        button.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0)';
        });
    });
});

// Modal enhancement
document.addEventListener('DOMContentLoaded', function() {
    // Add backdrop blur when modal opens
    const modals = document.querySelectorAll('[data-modal-toggle]');
    
    modals.forEach(trigger => {
        trigger.addEventListener('click', function() {
            const targetModal = document.getElementById(this.dataset.modalTarget);
            if (targetModal) {
                setTimeout(() => {
                    document.body.style.backdropFilter = 'blur(4px)';
                }, 100);
            }
        });
    });
    
    // Remove backdrop blur when modal closes
    const modalCloses = document.querySelectorAll('[data-modal-hide]');
    
    modalCloses.forEach(closeBtn => {
        closeBtn.addEventListener('click', function() {
            document.body.style.backdropFilter = 'none';
        });
    });
});

// Utility functions
function formatCurrency(amount) {
    return new Intl.NumberFormat('en-PH', {
        style: 'currency',
        currency: 'PHP'
    }).format(amount);
}

function formatDate(dateString) {
    return new Date(dateString).toLocaleDateString('en-PH', {
        year: 'numeric',
        month: 'short',
        day: 'numeric'
    });
}

// Export functions for global use
window.TransactionUtils = {
    formatCurrency,
    formatDate
};