/* Custom Card Styles */
.booking-card {
    transition: all 0.3s ease;
    border: 1px solid #e5e7eb;
    background: linear-gradient(135deg, #ffffff 0%, #f9fafb 100%);
}

.booking-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
    border-color: #d1d5db;
}

.action-button {
    transition: all 0.2s ease;
    padding: 0.5rem 1rem;
    border-radius: 0.5rem;
    font-size: 0.875rem;
    font-weight: 500;
    min-width: 80px;
    border: none;
    cursor: pointer;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    text-decoration: none;
}

.action-button:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.status-badge {
    display: inline-flex;
    align-items: center;
    padding: 0.25rem 0.75rem;
    border-radius: 9999px;
    font-size: 0.75rem;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

/* Status Styles */
.status-pending {
    background-color: #fef3c7;
    color: #d97706;
    border: 1px solid #fbbf24;
}

.status-ready {
    background-color: #dcfce7;
    color: #16a34a;
    border: 1px solid #bbf7d0;
}

.status-canceled {
    background-color: #fee2e2;
    color: #dc2626;
    border: 1px solid #f87171;
}

.status-declined {
    background-color: #fef2f2;
    color: #dc2626;
    border: 1px solid #fecaca;
}

.status-approved {
    background-color: #dcfce7;
    color: #16a34a;
    border: 1px solid #bbf7d0;
}

/* Button Styles */
.btn-view {
    background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
    color: white;
}

.btn-view:hover {
    background: linear-gradient(135deg, #1d4ed8 0%, #1e40af 100%);
}

.btn-cancel {
    background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
    color: white;
}

.btn-cancel:hover {
    background: linear-gradient(135deg, #dc2626 0%, #b91c1c 100%);
}

.btn-close.pending {
    background: linear-gradient(135deg, #6b7280 0%, #4b5563 100%);
    color: white;
}

.btn-submit.pending {
    background: linear-gradient(135deg, #059669 0%, #047857 100%);
    color: white;
}

/* Animation Classes */
.fade-in {
    animation: fadeIn 0.5s ease-in;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Modal Styles */
.modal-header-gradient {
    background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
}

.modal-header-gradient.green {
    background: linear-gradient(135deg, #059669 0%, #047857 100%);
}

.modal-header-gradient.red {
    background: linear-gradient(135deg, #dc2626 0%, #b91c1c 100%);
}

/* Responsive Design */
@media (max-width: 768px) {
    .booking-card {
        margin-bottom: 1rem;
    }

    .action-button {
        font-size: 0.75rem;
        padding: 0.375rem 0.75rem;
        min-width: 70px;
    }

    .status-badge {
        font-size: 0.625rem;
        padding: 0.125rem 0.5rem;
    }
}

.btn-close.pending:hover {
    background: linear-gradient(135deg, #4b5563 0%, #374151 100%);
}

.btn-submit.pending {
    background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
    color: white;
}

.btn-submit.pending:hover {
    background: linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%);
}

/* Approved Status Styles */
.status-approved {
    background-color: #dcfce7;
    color: #16a34a;
    border: 1px solid #22c55e;
}

.status-ready {
    background-color: #d1fae5;
    color: #059669;
    border: 1px solid #34d399;
}

.btn-close.approved {
    background: linear-gradient(135deg, #10b981 0%, #059669 100%);
    color: white;
}

.btn-close.approved:hover {
    background: linear-gradient(135deg, #059669 0%, #047857 100%);
}

.btn-submit.approved {
    background: linear-gradient(135deg, #10b981 0%, #059669 100%);
    color: white;
}

.btn-submit.approved:hover {
    background: linear-gradient(135deg, #059669 0%, #047857 100%);
}

/* Button Color Styles */