// Flowbite-compatible transaction page functionality

// Update booking count display
function updateBookingCount(pageType = 'pending') {
    const visibleCards = document.querySelectorAll('.booking-card:not([style*="display: none"])');
    const count = visibleCards.length;
    const countElement = document.getElementById('bookingCount');
    
    if (countElement) {
        if (pageType === 'approved') {
            countElement.textContent = `Showing ${count} approved booking${count !== 1 ? 's' : ''}`;
        } else {
            countElement.textContent = count;
        }
    }
}

// Search functionality with debouncing
let searchTimeout;
function searchBookings(pageType = 'pending') {
    clearTimeout(searchTimeout);
    searchTimeout = setTimeout(() => {
        const searchInput = document.getElementById('searchInput');
        if (!searchInput) return;
        
        const searchTerm = searchInput.value.toLowerCase();
        const bookingCards = document.querySelectorAll('.booking-card');
        
        bookingCards.forEach(card => {
            const reference = card.dataset.reference?.toLowerCase() || '';
            const resort = card.dataset.resort?.toLowerCase() || '';
            const status = card.dataset.status?.toLowerCase() || '';
            
            let isVisible;
            if (pageType === 'approved') {
                const operator = card.dataset.operator?.toLowerCase() || '';
                isVisible = reference.includes(searchTerm) || 
                           operator.includes(searchTerm) ||
                           resort.includes(searchTerm) ||
                           status.includes(searchTerm);
            } else {
                const boat = card.dataset.boat?.toLowerCase() || '';
                isVisible = reference.includes(searchTerm) || 
                           resort.includes(searchTerm) || 
                           boat.includes(searchTerm) ||
                           status.includes(searchTerm);
            }
            
            card.style.display = isVisible ? 'block' : 'none';
        });
        
        updateBookingCount(pageType);
    }, 300);
}

// Initialize transaction page functionality
function initializeTransactionPage(pageType = 'pending') {
    // Update initial count
    updateBookingCount(pageType);
    
    // Add search event listener
    const searchInput = document.getElementById('searchInput');
    if (searchInput) {
        searchInput.addEventListener('input', () => searchBookings(pageType));
        
        // Focus effect
        searchInput.addEventListener('focus', function() {
            this.parentElement.scrollIntoView({ behavior: 'smooth', block: 'center' });
        });
    }
    
    // Enhanced card hover effects using Tailwind classes
    const bookingCards = document.querySelectorAll('.booking-card');
    bookingCards.forEach(card => {
        card.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-4px)';
            this.style.transition = 'transform 0.2s ease-in-out';
        });
        
        card.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0)';
        });
    });
}

// Initialize when page loads
document.addEventListener('DOMContentLoaded', function() {
    // Detect page type based on URL or page content
    const pageType = window.location.pathname.includes('approved') ? 'approved' : 'pending';
    initializeTransactionPage(pageType);
});