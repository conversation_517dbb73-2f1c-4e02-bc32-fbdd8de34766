<?php
require '_header.php';
?>

<link rel="stylesheet" href="assets/transaction-styles.css">

<div class="p-4 sm:ml-64">
    <div class="mt-16 fade-in"> <!-- Adjusted for top navbar -->
        <!-- Header Section -->
        <div class="bg-gradient-to-r from-green-600 to-green-700 rounded-xl shadow-lg p-6 mb-8">
            <!-- Breadcrumb -->
            <nav class="flex mb-4" aria-label="Breadcrumb">
                <ol class="inline-flex items-center space-x-1 md:space-x-2">
                    <li class="inline-flex items-center">
                        <a href="home.php"
                            class="inline-flex items-center text-sm font-medium text-green-100 hover:text-white transition-colors">
                            <svg class="w-4 h-4 me-2" fill="currentColor" viewBox="0 0 20 20">
                                <path
                                    d="m19.707 9.293-2-2-7-7a1 1 0 0 0-1.414 0l-7 7-2 2a1 1 0 0 0 1.414 1.414L2 10.414V18a2 2 0 0 0 2 2h3a1 1 0 0 0 1-1v-4a1 1 0 0 1 1-1h2a1 1 0 0 1 1 1v4a1 1 0 0 0 1 1h3a2 2 0 0 0 2-2v-7.586l.293.293a1 1 0 0 0 1.414-1.414Z" />
                            </svg>
                            Home
                        </a>
                    </li>
                    <li>
                        <div class="flex items-center">
                            <svg class="w-3 h-3 text-green-200 mx-1" fill="none" viewBox="0 0 6 10">
                                <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round"
                                    stroke-width="2" d="m1 9 4-4-4-4" />
                            </svg>
                            <span class="text-sm font-medium text-green-100">Transactions</span>
                        </div>
                    </li>
                    <li>
                        <div class="flex items-center">
                            <svg class="w-3 h-3 text-green-200 mx-1" fill="none" viewBox="0 0 6 10">
                                <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round"
                                    stroke-width="2" d="m1 9 4-4-4-4" />
                            </svg>
                            <span class="text-sm font-medium text-white">Approved</span>
                        </div>
                    </li>
                </ol>
            </nav>

            <!-- Title Section -->
            <div class="flex items-center">
                <div class="bg-white bg-opacity-20 p-3 rounded-xl mr-4">
                    <i class="fas fa-check-circle text-white text-2xl"></i>
                </div>
                <div>
                    <h1 class="text-3xl font-bold text-white mb-1">Approved Bookings</h1>
                    <p class="text-green-100">Bookings that have been approved by operators</p>
                </div>
            </div>
        </div>

        <!-- Search and Filter -->
        <div class="flex flex-col sm:flex-row justify-between items-center mb-6 gap-4">
            <div class="relative flex-1 max-w-md">
                <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                    <i class="fas fa-search text-gray-400"></i>
                </div>
                <input type="text" id="searchInput" placeholder="Search bookings..."
                    class="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
            </div>
            <div class="flex items-center text-sm text-gray-600">
                <i class="fas fa-info-circle mr-2"></i>
                <span id="bookingCount">Showing bookings</span>
            </div>
        </div>

        <!-- Booking Cards Container -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6" id="bookingContainer">
            <?php
            try {
                $getBoatStatus = "Approved";
                $bookingDetails = getBookingDetails($pdo, $getBoatStatus, $id);

                if ($bookingDetails) {
                    foreach ($bookingDetails as $row) {
                        $operatorName = trim($row['firstname'] . ' ' . $row['middlename'] . ' ' . $row['lastname'] . ' ' . $row['extname']);
                        $extname = $row['extname'] ?? ''; // Use null coalescing operator for defaults
                        $middlename = $row['middlename'] ?? '';
                        $designationTour = $row['tour_operator_designation'];
                        $resortName = $row['resort_operator_designation'];
                        $referenceNumber = $row['referenceNum'];
                        $boatName = $row['boatName'];
                        $portName = $row['portName'];
                        $checkIn = $row['check_in_date'];
                        $checkout = $row['check_out_date'];
                        $adultCount = $row['total_adults'];
                        $childrenCount = $row['total_children'];
                        ?>
                        <!-- Booking Card -->
                        <div class="booking-card bg-white rounded-lg shadow-md border border-gray-200 p-4 hover:shadow-lg transition-all duration-200"
                            data-reference="<?= htmlspecialchars($referenceNumber ?? ''); ?>"
                            data-operator="<?= htmlspecialchars($operatorName ?? ''); ?>"
                            data-resort="<?= htmlspecialchars($row['resort_operator_designation'] ?? ''); ?>"
                            data-status="approved">
                            <!-- Reference Number -->
                            <div class="flex justify-between items-start mb-4">
                                <div class="flex-1">
                                    <h3 class="text-lg font-semibold text-gray-900 mb-2">
                                        <?= htmlspecialchars($row['referenceNum'] ?? 'N/A'); ?>
                                    </h3>
                                    <div class="status-badge status-ready">
                                        <i class="fas fa-clock mr-1"></i>
                                        Approved
                                    </div>
                                </div>
                            </div>

                            <!-- Details -->
                            <div class="space-y-3 mb-4">
                                <div class="flex items-center text-sm text-gray-600">
                                    <i class="fas fa-building text-gray-400 w-5 mr-3"></i>
                                    <span class="text-gray-600">Resort:</span>
                                    <span
                                        class="ml-2 font-medium text-gray-900"><?= htmlspecialchars($row['resort_operator_designation']); ?></span>
                                </div>
                                <div class="flex items-center text-sm text-gray-600">
                                    <i class="fas fa-ship text-gray-400 w-5 mr-3"></i>
                                    <span class="text-gray-600">Boat:</span>
                                    <span class="ml-2 font-medium text-gray-900"><?= htmlspecialchars($boatName ?? 'N/A'); ?></span>
                                </div>
                            </div>

                            <!-- Action Buttons -->
                            <div class="flex gap-2">
                                <button data-modal-target="view-booking-modal-<?= $row['booking_id']; ?>"
                                    data-modal-toggle="view-booking-modal-<?= $row['booking_id']; ?>" type="button"
                                    class="flex-1 inline-flex items-center justify-center px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white text-sm font-medium rounded-lg transition-colors duration-200">
                                    <i class="fas fa-eye mr-2"></i>
                                    View Details
                                </button>
                            </div>
                        </div>

                        <!-- View Booking Modal -->
                        <div id="view-booking-modal-<?= $row['booking_id']; ?>" tabindex="-1"
                            class="hidden overflow-y-auto overflow-x-hidden fixed top-0 right-0 left-0 z-50 justify-center items-center w-full md:inset-0 h-[calc(100%-1rem)] max-h-full">
                            <div class="relative w-full max-w-4xl max-h-full">
                                <!-- Modal Container -->
                                <div class="relative bg-white rounded-lg shadow-lg border border-gray-200">
                                    <!-- Modal Header -->
                                    <div
                                        class="flex items-center justify-between p-3 bg-gradient-to-r from-green-600 to-green-800 rounded-t-lg">
                                        <div class="flex items-center">
                                            <i class="fas fa-ship text-white mr-2"></i>
                                            <h3 class="text-lg font-semibold text-white">
                                                Approved Boat Booking
                                            </h3>
                                        </div>
                                        <button type="button"
                                            class="text-white hover:text-gray-200 rounded-full w-7 h-7 flex justify-center items-center transition-colors duration-200 bg-green-800 hover:bg-green-900"
                                            data-modal-hide="view-booking-modal-<?= $row['booking_id']; ?>">
                                            <svg class="w-4 h-4" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none"
                                                viewBox="0 0 14 14">
                                                <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round"
                                                    stroke-width="2" d="m1 1 6 6m0 0 6 6M7 7l6-6M7 7l-6 6" />
                                            </svg>
                                            <span class="sr-only">Close modal</span>
                                        </button>
                                    </div>

                                    <!-- Reference Number Banner -->
                                    <div class="bg-green-50 p-3 border-b border-green-100">
                                        <div class="flex justify-between items-center">
                                            <div class="flex items-center">
                                                <i class="fas fa-hashtag text-green-600 mr-2"></i>
                                                <span class="text-sm font-medium text-green-800">Reference Number:</span>
                                            </div>
                                            <span class="text-sm font-bold text-green-600"><?= $referenceNumber; ?></span>
                                        </div>
                                    </div>

                                    <!-- Modal Body -->
                                    <div class="p-3 bg-white">
                                        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3">
                                            <!-- Operator & Destination Information -->
                                            <div class="bg-gray-50 p-3 rounded-lg border border-gray-200">
                                                <div class="flex items-center mb-2">
                                                    <i class="fas fa-user-tie text-green-600 mr-2"></i>
                                                    <h4 class="text-sm font-semibold text-gray-800">Operator & Destination</h4>
                                                </div>
                                                <div class="space-y-2 text-xs">
                                                    <div class="flex justify-between">
                                                        <span class="text-gray-600">Tour Operator:</span>
                                                        <span class="font-medium text-gray-900"><?= $designationTour; ?></span>
                                                    </div>
                                                    <div class="flex justify-between">
                                                        <span class="text-gray-600">Designation:</span>
                                                        <span class="font-medium text-gray-900"><?= $operatorName; ?></span>
                                                    </div>
                                                    <div class="flex justify-between">
                                                        <span class="text-gray-600">Resort Destination:</span>
                                                        <span class="font-medium text-gray-900"><?= $resortName; ?></span>
                                                    </div>
                                                    <div class="flex justify-between">
                                                        <span class="text-gray-600">Place of Departure:</span>
                                                        <span class="font-medium text-gray-900"><?= $portName; ?></span>
                                                    </div>
                                                </div>
                                            </div>

                                            <!-- Transportation & Schedule -->
                                            <div class="bg-gray-50 p-3 rounded-lg border border-gray-200">
                                                <div class="flex items-center mb-2">
                                                    <i class="fas fa-ship text-green-600 mr-2"></i>
                                                    <h4 class="text-sm font-semibold text-gray-800">Transportation & Schedule</h4>
                                                </div>
                                                <div class="space-y-2 text-xs">
                                                    <div class="flex justify-between">
                                                        <span class="text-gray-600">Boat Used:</span>
                                                        <span class="font-medium text-gray-900"><?= $boatName; ?></span>
                                                    </div>
                                                    <div class="flex justify-between">
                                                        <span class="text-gray-600">Check-in Date:</span>
                                                        <span
                                                            class="font-medium text-gray-900"><?= (new DateTime($checkIn))->format('M d, Y'); ?></span>
                                                    </div>
                                                    <div class="flex justify-between">
                                                        <span class="text-gray-600">Check-out Date:</span>
                                                        <span
                                                            class="font-medium text-gray-900"><?= (new DateTime($checkout))->format('M d, Y'); ?></span>
                                                    </div>
                                                </div>
                                            </div>

                                            <!-- Passenger Information -->
                                            <div class="bg-gray-50 p-3 rounded-lg border border-gray-200">
                                                <div class="flex items-center mb-2">
                                                    <i class="fas fa-users text-green-600 mr-2"></i>
                                                    <h4 class="text-sm font-semibold text-gray-800">Passenger Information</h4>
                                                </div>
                                                <div class="space-y-2 text-xs">
                                                    <div class="flex justify-between">
                                                        <span class="text-gray-600">Adults:</span>
                                                        <span class="font-medium text-gray-900"><?= $adultCount; ?></span>
                                                    </div>
                                                    <div class="flex justify-between">
                                                        <span class="text-gray-600">Children:</span>
                                                        <span class="font-medium text-gray-900"><?= $childrenCount; ?></span>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Modal Footer -->
                                    <div class="grid grid-cols-1 gap-2 p-3 bg-gray-50 border-t border-gray-200 rounded-b-lg">
                                        <button data-modal-hide="view-booking-modal-<?= $row['booking_id']; ?>" type="button"
                                            class="action-button btn-close approved">
                                            <i class="fas fa-times-circle mr-1.5"></i> Close
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <?php
                    }
                }
            } catch (PDOException $e) {
                ?>
                <div class="bg-red-50 border border-red-200 rounded-lg p-6 text-center">
                    <div class="bg-red-100 p-3 rounded-full mb-3 inline-block">
                        <i class="fas fa-exclamation-circle text-red-500 text-xl"></i>
                    </div>
                    <p class="text-red-600 font-medium">
                        Error: <?= htmlspecialchars($e->getMessage()); ?>
                    </p>
                </div>
                <?php
            }

            if (empty($bookingDetails)) {
                ?>
                <div class="col-span-full">
                    <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-8 text-center">
                        <div class="max-w-md mx-auto">
                            <div class="bg-green-100 p-4 rounded-full inline-block mb-4">
                                <i class="fas fa-check-circle text-green-400 text-2xl"></i>
                            </div>
                            <h3 class="text-lg font-semibold text-gray-900 mb-2">No Approved Bookings</h3>
                            <p class="text-gray-500 mb-4">There are currently no approved bookings.</p>
                        </div>
                    </div>
                </div>
                <?php
            }
            ?>
        </div>
    </div>
</div>

<?php
require '_footer.php';
?>