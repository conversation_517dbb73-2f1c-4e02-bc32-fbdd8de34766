<?php

function getAllCancellationRequests(PDO $pdo, $getBookingStatus)
{
    try {
        $sql = "SELECT
                    cbc.booking_id AS booking_id,
                    cbc.referenceNum,
                    cbc.date_created,
                    oi.designation,
                    bob.boatName
                FROM cb_booking_cancellation cbc
                JOIN operator_info oi
                    ON cbc.resort_operator_id = oi.user_id
                JOIN boat_operator_boatlist bob
                    ON cbc.boat_id = bob.id
                WHERE cbc.booking_status = :booking_status
                ORDER BY cbc.date_created DESC";

        $stmt = $pdo->prepare($sql);
        $stmt->bindParam(':booking_status', $getBookingStatus, PDO::PARAM_STR);
        $stmt->execute();

        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    } catch (PDOException $e) {
        error_log("Error fetching cancellation requests: " . $e->getMessage());
        return false;
    }
}

function getAllBookingDetails(PDO $pdo, $booking_status)
{
    try {
        $sql = "SELECT
                    bb.booking_id AS booking_id,
                    bb.referenceNum,
                    bb.check_in_date,
                    bb.check_out_date,
                    oi.designation,
                    bob.boatName,
                    cba.resort,
                    cba.boat,
                    cba.treasurer,
                    cba.mtho,
                    cba.date_updated AS time_declined,
                    cp.payment_status,
                    cp.total_adults,
                    cp.total_children,
                    cbc.booking_status AS cancellation_booking_status,                     
                    CASE
                        WHEN cba.resort = 'Declined' THEN 'Resort Operator'
                        WHEN cba.boat = 'Declined' THEN 'Boat Operator'                        
                        WHEN cba.mtho = 'Declined' THEN 'MTHO'
                        ELSE 'Unknown'
                    END AS declined_by
                FROM cb_bookings bb
                JOIN operator_info oi
                    ON bb.resort_operator_id = oi.user_id
                JOIN cb_payments cp
                    ON bb.booking_id = cp.booking_id
                JOIN boat_operator_boatlist bob
                    ON bb.boat_id = bob.id
                JOIN cb_booking_approvals cba
                    ON bb.booking_id = cba.booking_id
                LEFT JOIN cb_booking_cancellation cbc 
                    ON bb.booking_id = cbc.booking_id 
                    AND cbc.booking_status = 'request'
                WHERE bb.booking_status = :booking_status";

        $stmt = $pdo->prepare($sql);
        $stmt->bindParam(':booking_status', $booking_status, PDO::PARAM_STR);
        $stmt->execute();

        // Fetch all rows
        $rows = $stmt->fetchAll(PDO::FETCH_ASSOC);

        return $rows ?: []; // Return empty array if no rows found
    } catch (PDOException $e) {
        error_log("Database error: " . $e->getMessage());
        return null;
    }
}

function getBookingDetails(PDO $pdo, $getBookingId)
{
    try {
        $sql = "SELECT bb.booking_id AS booking_id,
                bb.booking_status AS booking_status,
                bb.referenceNum,
                bb.check_in_date,
                bb.check_out_date,
                bb.tour_operator_id,
                bb.control_number,
                oa.login_status,
                oi.designation AS resort_operator_designation,
                oi2.designation AS tour_operator_designation,
                oi2.firstname,
                oi2.middlename,
                oi2.lastname,
                oi2.extname,
                bob.boatName,
                pl.portName,
                cp.total_adults,
                cp.total_children,
                cp.total_crew,
                cp.port_fee,
                cp.payment_status,
                cp.payment_method,
                cp.total_amount,
                cp.or_num,
                cp.voucher_use,
                cv.voucher_vinzons,
                cv.voucher_others,
                cba.resort,
                cba.boat,
                cbc.booking_status AS cancellation_booking_status
        FROM cb_bookings bb
        LEFT JOIN operator_info oi ON bb.resort_operator_id = oi.user_id
        LEFT JOIN operator_info oi2 ON bb.tour_operator_id = oi2.user_id
        LEFT JOIN operator_account oa ON oi2.user_id = oa.id
        LEFT JOIN boat_operator_boatlist bob ON bb.boat_id = bob.id
        LEFT JOIN port_list pl ON bb.port_id = pl.id
        LEFT JOIN cb_payments cp ON bb.booking_id = cp.booking_id
        LEFT JOIN cb_booking_approvals cba ON bb.booking_id = cba.booking_id
        LEFT JOIN cb_vouchers cv ON bb.tour_operator_id = cv.operator_id
        LEFT JOIN cb_booking_cancellation cbc 
                ON bb.booking_id = cbc.booking_id 
                AND cbc.booking_status = 'request'
        WHERE bb.booking_id = :booking_id";

        $stmt = $pdo->prepare($sql);
        $stmt->bindParam(':booking_id', $getBookingId, PDO::PARAM_STR);
        $stmt->execute();

        // Fetch a single row
        $row = $stmt->fetch(PDO::FETCH_ASSOC);

        return $row ?: null; // Return null if no rows are found
    } catch (PDOException $e) {
        error_log("Database error: " . $e->getMessage());
        return null;
    }
}

function getTouristAndCrewCounts(PDO $pdo, $getBookingId)
{
    $stmt = $pdo->prepare("
        SELECT
            SUM(CASE WHEN age >= 9 AND info_type = 'tourist' THEN 1 ELSE 0 END) AS adult_count,
            SUM(CASE WHEN age <= 8 AND info_type = 'tourist' THEN 1 ELSE 0 END) AS children_count,
            COUNT(CASE WHEN info_type = 'crewTts' THEN 1 ELSE NULL END) AS crew_count
        FROM cb_tourists
        WHERE info_type IN ('tourist', 'crewTts')
        AND booking_id = :booking_id
    ");
    $stmt->bindParam(':booking_id', $getBookingId, PDO::PARAM_INT);
    $stmt->execute();
    $result = $stmt->fetch(PDO::FETCH_ASSOC);

    return [
        'adults' => $result['adult_count'] ?? 0,
        'children' => $result['children_count'] ?? 0,
        'crewTts' => $result['crew_count'] ?? 0
    ];
}

function getDistinctTourist(PDO $pdo, $parameter, $getBookingId)
{
    // Make sure $parameter is a valid column name to prevent SQL injection
    $allowedColumns = ["contact_number", "address"];
    if (!in_array($parameter, $allowedColumns)) {
        // Handle invalid parameter error
        return false;
    }

    $sql = "SELECT DISTINCT $parameter FROM cb_tourists WHERE booking_id = :booking_id";
    $stmt = $pdo->prepare($sql);
    $stmt->bindParam(':booking_id', $getBookingId, PDO::PARAM_INT);
    $stmt->execute();
    $result = $stmt->fetchAll(PDO::FETCH_ASSOC);
    $stmt->closeCursor();

    return $result;
}

function getNotification(PDO $pdo): array
{
    $stmt = $pdo->prepare("
        SELECT
            SUM(booking_status = 'draft') AS draft_count,
            SUM(booking_status = 'voucher') AS voucher_count,
            SUM(booking_status = 'pending') AS pending_count,
            SUM(booking_status = 'approved') AS approved_count,
            SUM(booking_status = 'declined') AS declined_count,
            SUM(booking_status = 'completed') AS completed_count
        FROM cb_bookings
    ");
    $stmt->execute();
    $result = $stmt->fetch(PDO::FETCH_ASSOC);

    return [
        'draft' => (int) ($result['draft_count'] ?? 0),
        'voucher' => (int) ($result['voucher_count'] ?? 0),
        'pending' => (int) ($result['pending_count'] ?? 0),
        'approved' => (int) ($result['approved_count'] ?? 0),
        'declined' => (int) ($result['declined_count'] ?? 0),
        'completed' => (int) ($result['completed_count'] ?? 0)
    ];
}
