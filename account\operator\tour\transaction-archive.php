<?php
require '_header.php';
?>

<link rel="stylesheet" href="assets/transaction-styles.css">


<div class="p-4 sm:ml-64">
    <div class="mt-16 fade-in"> <!-- Adjusted for top navbar -->
        <!-- Header Section -->
        <div class="bg-gradient-to-r from-purple-600 to-purple-700 rounded-xl shadow-lg p-6 mb-8">
            <!-- Breadcrumb -->
            <nav class="flex mb-4" aria-label="Breadcrumb">
                <ol class="inline-flex items-center space-x-1 md:space-x-2">
                    <li class="inline-flex items-center">
                        <a href="home.php"
                            class="inline-flex items-center text-sm font-medium text-purple-100 hover:text-white transition-colors">
                            <svg class="w-4 h-4 me-2" fill="currentColor" viewBox="0 0 20 20">
                                <path
                                    d="m19.707 9.293-2-2-7-7a1 1 0 0 0-1.414 0l-7 7-2 2a1 1 0 0 0 1.414 1.414L2 10.414V18a2 2 0 0 0 2 2h3a1 1 0 0 0 1-1v-4a1 1 0 0 1 1-1h2a1 1 0 0 1 1 1v4a1 1 0 0 0 1 1h3a2 2 0 0 0 2-2v-7.586l.293.293a1 1 0 0 0 1.414-1.414Z" />
                            </svg>
                            Home
                        </a>
                    </li>
                    <li>
                        <div class="flex items-center">
                            <svg class="w-3 h-3 text-purple-200 mx-1" fill="none" viewBox="0 0 6 10">
                                <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round"
                                    stroke-width="2" d="m1 9 4-4-4-4" />
                            </svg>
                            <span class="text-sm font-medium text-purple-100">Transactions</span>
                        </div>
                    </li>
                    <li>
                        <div class="flex items-center">
                            <svg class="w-3 h-3 text-purple-200 mx-1" fill="none" viewBox="0 0 6 10">
                                <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round"
                                    stroke-width="2" d="m1 9 4-4-4-4" />
                            </svg>
                            <span class="text-sm font-medium text-white">Archive</span>
                        </div>
                    </li>
                </ol>
            </nav>

            <!-- Title Section -->
            <div class="flex items-center">
                <div class="bg-white bg-opacity-20 p-3 rounded-xl mr-4">
                    <i class="fas fa-archive text-white text-2xl"></i>
                </div>
                <div>
                    <h1 class="text-3xl font-bold text-white mb-1">Archived Bookings</h1>
                    <p class="text-purple-100">Completed and archived booking records</p>
                </div>
            </div>
        </div>

        <!-- Search and Filter Section -->
        <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-4 mb-6">
            <div class="flex flex-col sm:flex-row gap-4 items-center justify-between">
                <div class="relative flex-1 max-w-md">
                    <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                        <i class="fas fa-search text-gray-400"></i>
                    </div>
                    <input type="text" id="searchInput" placeholder="Search bookings..."
                        class="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500 transition-colors">
                </div>
                <div class="flex items-center gap-2 text-sm text-gray-600">
                    <i class="fas fa-info-circle"></i>
                    <span id="bookingCount">Loading bookings...</span>
                </div>
            </div>
        </div>

        <!-- Bookings Grid -->
        <div id="bookings-container" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 fade-in">
            <?php
            try {
                $booking_status = "completed";

                $bookingDetails = getAllBookingDetails($pdo, $booking_status);

                if ($bookingDetails) {
                    foreach ($bookingDetails as $row) {
                        ?>
                        <div class="booking-card bg-white rounded-xl shadow-lg border border-gray-200 p-6 hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1"
                            data-reference="<?= htmlspecialchars($row['referenceNum'] ?? ''); ?>"
                            data-resort="<?= htmlspecialchars($row['designation'] ?? ''); ?>"
                            data-boat="<?= htmlspecialchars($row['boatName'] ?? ''); ?>" data-status="archived">
                            <!-- Card Header -->
                            <div class="flex justify-between items-start mb-4">
                                <div class="flex-1">
                                    <h3 class="text-lg font-semibold text-gray-900 mb-2">
                                        <?= htmlspecialchars($row['referenceNum'] ?? 'N/A'); ?>
                                    </h3>
                                    <div
                                        class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-purple-100 text-purple-800">
                                        <i class="fas fa-archive mr-1"></i>
                                        Archived
                                    </div>
                                </div>
                            </div>

                            <!-- Booking Details -->
                            <div class="space-y-3 mb-4">
                                <div class="flex items-center text-sm">
                                    <i class="fas fa-hotel text-gray-400 w-4 mr-3"></i>
                                    <span class="text-gray-600">Resort:</span>
                                    <span
                                        class="ml-2 font-medium text-gray-900"><?= htmlspecialchars($row['designation']); ?></span>
                                </div>
                                <div class="flex items-center text-sm">
                                    <i class="fas fa-ship text-gray-400 w-4 mr-3"></i>
                                    <span class="text-gray-600">Boat:</span>
                                    <span class="ml-2 font-medium text-gray-900"><?= htmlspecialchars($row['boatName']); ?></span>
                                </div>
                            </div>

                            <!-- Action Buttons -->
                            <div class="grid grid-cols-2 gap-2 pt-4 border-t border-gray-100">
                                <button data-modal-target="view-details-modal-<?= $row['booking_id']; ?>"
                                    data-modal-toggle="view-details-modal-<?= $row['booking_id']; ?>" type="button"
                                    class="action-button bg-blue-600 hover:bg-blue-700 text-white">
                                    <i class="fas fa-eye mr-2"></i>
                                    View Details
                                </button>
                                <button data-modal-target="restore-booking-modal-<?= $row['booking_id']; ?>"
                                    data-modal-toggle="restore-booking-modal-<?= $row['booking_id']; ?>" type="button"
                                    class="action-button bg-green-600 hover:bg-green-700 text-white">
                                    <i class="fas fa-undo mr-2"></i>
                                    Restore
                                </button>
                                <button data-modal-target="delete-booking-modal-<?= $row['booking_id']; ?>"
                                    data-modal-toggle="delete-booking-modal-<?= $row['booking_id']; ?>" type="button"
                                    class="action-button col-span-2 bg-red-600 hover:bg-red-700 text-white">
                                    <i class="fas fa-trash mr-2"></i>
                                    Delete Permanently
                                </button>
                            </div>
                        </div>
                        <!-- View Passenger Modal -->
                        <div id="view-approvals-modal-<?= $row['booking_id']; ?>" tabindex="-1"
                            class="hidden overflow-y-auto overflow-x-hidden fixed top-0 right-0 left-0 z-50 justify-center items-center w-full md:inset-0 h-[calc(100%-1rem)] max-h-full">
                            <div class="relative w-full max-w-sm max-h-full"> <!-- Reduced max-width to max-w-sm -->
                                <!-- Modal Container -->
                                <div class="relative bg-white rounded-lg shadow-lg">
                                    <!-- Modal Header -->
                                    <div
                                        class="flex items-center justify-between p-4 bg-gradient-to-r from-green-600 to-green-500 rounded-t-lg">
                                        <h3 class="text-xl font-semibold text-white"> <!-- Reduced text size to text-xl -->
                                            Download File
                                        </h3>
                                        <button type="button"
                                            class="text-white hover:text-gray-200 rounded-full w-6 h-6 flex justify-center items-center transition-colors duration-200"
                                            data-modal-hide="view-approvals-modal-<?= $row['booking_id']; ?>" type="button"
                                            class="py-2 px-4 text-sm font-semibold text-white bg-gray-600 rounded-md hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-gray-400 transition-all duration-200">
                                            <svg class="w-4 h-4" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none"
                                                viewBox="0 0 14 14">
                                                <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round"
                                                    stroke-width="2" d="m1 1 6 6m0 0 6 6M7 7l6-6M7 7l-6 6" />
                                            </svg>
                                            <span class="sr-only">Close modal</span>
                                        </button>
                                    </div>
                                    <!-- Modal Body -->
                                    <div class="p-4 bg-gray-50">
                                        <!-- Passenger Info -->
                                        <div class="space-y-3">
                                            <div
                                                class="bg-gray-800 text-white p-6 rounded-lg shadow-lg flex justify-between items-center">
                                                <div>
                                                    <p class="text-sm font-semibold uppercase tracking-wider">Tourism Pass</p>
                                                </div>
                                                <a href="download/tourism-pass.php?id=<?= $row['booking_id']; ?>" type="button"
                                                    class="inline-flex items-center justify-center bg-green-600 hover:bg-green-700 text-white font-medium rounded-md text-xs px-3 py-1.5">
                                                    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24"
                                                        stroke-width="1.5" stroke="currentColor" class="w-4 h-4">
                                                        <path stroke-linecap="round" stroke-linejoin="round"
                                                            d="M3 16.5v2.25A2.25 2.25 0 0 0 5.25 21h13.5A2.25 2.25 0 0 0 21 18.75V16.5M16.5 12 12 16.5m0 0L7.5 12m4.5 4.5V3" />
                                                    </svg>
                                                </a>
                                            </div>
                                            <div
                                                class="bg-gray-800 text-white p-6 rounded-lg shadow-lg flex justify-between items-center">
                                                <div>
                                                    <p class="text-sm font-semibold uppercase tracking-wider">Manifesto</p>
                                                </div>
                                                <a href="download/manifesto.php?id=<?= $row['booking_id']; ?>" type="button"
                                                    class="inline-flex items-center justify-center bg-green-600 hover:bg-green-700 text-white font-medium rounded-md text-xs px-3 py-1.5">
                                                    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24"
                                                        stroke-width="1.5" stroke="currentColor" class="w-4 h-4">
                                                        <path stroke-linecap="round" stroke-linejoin="round"
                                                            d="M3 16.5v2.25A2.25 2.25 0 0 0 5.25 21h13.5A2.25 2.25 0 0 0 21 18.75V16.5M16.5 12 12 16.5m0 0L7.5 12m4.5 4.5V3" />
                                                    </svg>
                                                </a>
                                            </div>
                                        </div>
                                    </div>
                                    <!-- Modal Footer -->
                                    <div class="flex justify-end items-center p-4 bg-gray-50 border-t border-gray-100 rounded-b-lg">
                                        <button data-modal-hide="view-approvals-modal-<?= $row['booking_id']; ?>" type="button"
                                            class="py-2 px-4 text-sm font-semibold text-white bg-red-500 rounded-md hover:bg-red-600 focus:outline-none focus:ring-2 focus:ring-red-400 transition-all duration-200">
                                            Close
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <?php
                    }
                } else {
                    // No archived bookings found
                    ?>
                    <div class="col-span-full">
                        <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-8 text-center">
                            <div class="max-w-md mx-auto">
                                <div class="bg-gray-100 p-4 rounded-full inline-block mb-4">
                                    <i class="fas fa-archive text-gray-400 text-2xl"></i>
                                </div>
                                <h3 class="text-lg font-semibold text-gray-900 mb-2">No Archived Bookings</h3>
                                <p class="text-gray-500 mb-4">There are currently no archived bookings to display.</p>
                                <a href="home.php"
                                    class="inline-flex items-center px-4 py-2 bg-purple-600 hover:bg-purple-700 text-white font-medium rounded-lg transition-colors">
                                    <i class="fas fa-arrow-left mr-2"></i>
                                    Back to Dashboard
                                </a>
                            </div>
                        </div>
                    </div>
                    <?php
                }
            } catch (PDOException $e) {
                ?>
                <div class="col-span-full">
                    <div class="bg-white rounded-xl shadow-sm border border-red-200 p-8 text-center">
                        <div class="max-w-md mx-auto">
                            <div class="bg-red-100 p-4 rounded-full inline-block mb-4">
                                <i class="fas fa-exclamation-triangle text-red-400 text-2xl"></i>
                            </div>
                            <h3 class="text-lg font-semibold text-gray-900 mb-2">Error Loading Bookings</h3>
                            <p class="text-gray-500 mb-4">There was an error loading the archived bookings:
                                <?= htmlspecialchars($e->getMessage()); ?>
                            </p>
                            <button onclick="window.location.reload()"
                                class="inline-flex items-center px-4 py-2 bg-gray-600 hover:bg-gray-700 text-white font-medium rounded-lg transition-colors">
                                <i class="fas fa-refresh mr-2"></i>
                                Try Again
                            </button>
                        </div>
                    </div>
                </div>
                <?php
            }
            ?>
        </div>
    </div>

    <?php
    require '_footer.php';
    ?>