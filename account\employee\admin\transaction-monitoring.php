<?php
require '_header.php';
?>

<link rel="stylesheet" href="assets/transaction-styles.css">

<div class="p-4 sm:ml-64">
    <div class="mt-16 fade-in"> <!-- Adjusted for top navbar -->
        <!-- Header Section -->
        <div class="bg-gradient-to-r from-purple-600 to-purple-700 rounded-xl shadow-lg p-6 mb-8">
            <!-- Breadcrumb -->
            <nav class="flex mb-4" aria-label="Breadcrumb">
                <ol class="inline-flex items-center space-x-1 md:space-x-2">
                    <li class="inline-flex items-center">
                        <a href="home.php"
                            class="inline-flex items-center text-sm font-medium text-purple-100 hover:text-white transition-colors">
                            <svg class="w-4 h-4 me-2" fill="currentColor" viewBox="0 0 20 20">
                                <path
                                    d="m19.707 9.293-2-2-7-7a1 1 0 0 0-1.414 0l-7 7-2 2a1 1 0 0 0 1.414 1.414L2 10.414V18a2 2 0 0 0 2 2h3a1 1 0 0 0 1-1v-4a1 1 0 0 1 1-1h2a1 1 0 0 1 1 1v4a1 1 0 0 0 1 1h3a2 2 0 0 0 2-2v-7.586l.293.293a1 1 0 0 0 1.414-1.414Z" />
                            </svg>
                            Home
                        </a>
                    </li>
                    <li>
                        <div class="flex items-center">
                            <svg class="rtl:rotate-180 w-3 h-3 text-purple-300 mx-1" fill="none" viewBox="0 0 6 10">
                                <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round"
                                    stroke-width="2" d="m1 9 4-4-4-4" />
                            </svg>
                            <span class="text-sm font-medium text-white">Monitoring</span>
                        </div>
                    </li>
                </ol>
            </nav>

            <!-- Title Section -->
            <div class="flex items-center">
                <div class="bg-white bg-opacity-20 p-3 rounded-xl mr-4">
                    <i class="fas fa-chart-line text-white text-2xl"></i>
                </div>
                <div>
                    <h1 class="text-3xl font-bold text-white mb-1">Booking Monitoring</h1>
                    <p class="text-purple-100">Monitor and manage bookings awaiting MTHO approval</p>
                </div>
            </div>
        </div>

        <!-- Content Section -->
        <div class="bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden">
            <div class="overflow-x-auto">
                <table id="search-table" class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50">
                        <tr>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Reference #</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Operator Name</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Resort</th>
                            <th class="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">Boat</th>
                            <th class="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">Action</th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                    <?php
                    try {
                        $getMthoStatus = "Waiting";
                        $getBookingStatus = "pending";

                        $bookingDetails = getBookingDetails($pdo, $getMthoStatus, $getBookingStatus);

                        if ($bookingDetails) {
                            foreach ($bookingDetails as $row) {
                                $operatorName = trim($row['firstname'] . ' ' . $row['middlename'] . ' ' . $row['lastname'] . ' ' . $row['extname']);
                                $extname = $row['extname'] ?? ''; // Use null coalescing operator for defaults
                                $middlename = $row['middlename'] ?? '';
                                $designationTour = $row['tour_operator_designation'];
                                $resortName = $row['resort_operator_designation'];
                                $referenceNumber = $row['referenceNum'];
                                $boatName = $row['boatName'];
                                $portName = $row['portName'];
                                $checkIn = $row['check_in_date'];
                                $checkout = $row['check_out_date'];
                                $adultCount = $row['total_adults'];
                                $childrenCount = $row['total_children'];
                                $crewCount = $row['total_crew'];
                    ?>
                                <tr class="hover:bg-gray-50 transition-colors duration-200">
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-blue-600"><?= htmlspecialchars($referenceNumber); ?></td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900"><?= htmlspecialchars($operatorName); ?></td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500"><?= htmlspecialchars($row['resort_operator_designation']); ?></td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 text-center"><?= htmlspecialchars($row['boatName']); ?></td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-center">
                                        <button data-modal-target="view-approvals-modal-<?= $row['booking_id']; ?>" data-modal-toggle="view-approvals-modal-<?= $row['booking_id']; ?>" type="button" class="action-button bg-purple-600 hover:bg-purple-700 text-white">
                                            <i class="fas fa-chart-line mr-2"></i>
                                            Monitor
                                        </button>
                                    </td>
                                </tr>
                                <!-- View Booking Modal -->
                                <div id="view-approvals-modal-<?= $row['booking_id']; ?>" tabindex="-1" class="hidden overflow-y-auto overflow-x-hidden fixed top-0 right-0 left-0 z-50 justify-center items-center w-full md:inset-0 h-[calc(100%-1rem)] max-h-full">
                                    <div class="relative w-full max-w-4xl max-h-full">
                                        <!-- Modal Container -->
                                        <div class="relative bg-white rounded-lg shadow-lg border border-gray-200">
                                            <!-- Modal Header -->
                                            <div class="flex items-center justify-between p-3 bg-gradient-to-r from-purple-600 to-purple-800 rounded-t-lg">
                                                <div class="flex items-center">
                                                    <i class="fas fa-chart-line text-white mr-2"></i>
                                                    <h3 class="text-lg font-semibold text-white">
                                                        Booking Monitoring Details
                                                    </h3>
                                                </div>
                                                <button type="button" class="text-white hover:text-gray-200 rounded-full w-7 h-7 flex justify-center items-center transition-colors duration-200 bg-purple-800 hover:bg-purple-900" data-modal-hide="view-approvals-modal-<?= $row['booking_id']; ?>">
                                                    <svg class="w-4 h-4" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 14 14">
                                                        <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="m1 1 6 6m0 0 6 6M7 7l6-6M7 7l-6 6" />
                                                    </svg>
                                                    <span class="sr-only">Close modal</span>
                                                </button>
                                            </div>

                                            <!-- Reference Number Banner -->
                                            <div class="bg-blue-50 p-3 border-b border-blue-100">
                                                <div class="flex justify-between items-center">
                                                    <div class="flex items-center">
                                                        <i class="fas fa-hashtag text-blue-600 mr-2"></i>
                                                        <span class="text-sm font-medium text-blue-800">Reference Number:</span>
                                                    </div>
                                                    <span class="text-sm font-bold text-blue-600"><?= $referenceNumber; ?></span>
                                                </div>
                                            </div>

                                            <!-- Modal Body -->
                                            <div class="p-4 bg-white">
                                                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                                    <!-- Operator Information Section -->
                                                    <div class="bg-gray-50 p-4 rounded-lg border border-gray-200">
                                                        <div class="flex items-center mb-3">
                                                            <i class="fas fa-user-tie text-blue-600 mr-2"></i>
                                                            <h4 class="text-sm font-semibold text-gray-800">Operator Information</h4>
                                                        </div>
                                                        <div class="space-y-3">
                                                            <div class="bg-white p-3 rounded-md shadow-sm border border-gray-200">
                                                                <div class="flex items-center">
                                                                    <i class="fas fa-user text-gray-500 mr-2 text-xs"></i>
                                                                    <p class="text-xs font-medium text-gray-700">Tour Operator:</p>
                                                                </div>
                                                                <p class="text-xs text-gray-600 mt-1 pl-5"><?= $operatorName; ?></p>
                                                            </div>
                                                            <div class="bg-white p-3 rounded-md shadow-sm border border-gray-200">
                                                                <div class="flex items-center">
                                                                    <i class="fas fa-id-badge text-gray-500 mr-2 text-xs"></i>
                                                                    <p class="text-xs font-medium text-gray-700">Designation:</p>
                                                                </div>
                                                                <p class="text-xs text-gray-600 mt-1 pl-5"><?= $designationTour; ?></p>
                                                            </div>
                                                        </div>
                                                    </div>

                                                    <!-- Destination Information Section -->
                                                    <div class="bg-gray-50 p-4 rounded-lg border border-gray-200">
                                                        <div class="flex items-center mb-3">
                                                            <i class="fas fa-map-marker-alt text-blue-600 mr-2"></i>
                                                            <h4 class="text-sm font-semibold text-gray-800">Destination Information</h4>
                                                        </div>
                                                        <div class="space-y-3">
                                                            <div class="bg-white p-3 rounded-md shadow-sm border border-gray-200">
                                                                <div class="flex items-center">
                                                                    <i class="fas fa-hotel text-gray-500 mr-2 text-xs"></i>
                                                                    <p class="text-xs font-medium text-gray-700">Resort Destination:</p>
                                                                </div>
                                                                <p class="text-xs text-gray-600 mt-1 pl-5"><?= $resortName; ?></p>
                                                            </div>
                                                            <div class="bg-white p-3 rounded-md shadow-sm border border-gray-200">
                                                                <div class="flex items-center">
                                                                    <i class="fas fa-ship text-gray-500 mr-2 text-xs"></i>
                                                                    <p class="text-xs font-medium text-gray-700">Boat to be Used:</p>
                                                                </div>
                                                                <p class="text-xs text-gray-600 mt-1 pl-5"><?= $boatName; ?></p>
                                                            </div>
                                                            <div class="bg-white p-3 rounded-md shadow-sm border border-gray-200">
                                                                <div class="flex items-center">
                                                                    <i class="fas fa-anchor text-gray-500 mr-2 text-xs"></i>
                                                                    <p class="text-xs font-medium text-gray-700">Place of Departure:</p>
                                                                </div>
                                                                <p class="text-xs text-gray-600 mt-1 pl-5"><?= $portName; ?></p>
                                                            </div>
                                                        </div>
                                                    </div>

                                                    <!-- Trip Details Section -->
                                                    <div class="bg-gray-50 p-4 rounded-lg border border-gray-200 md:col-span-2">
                                                        <div class="flex items-center mb-3">
                                                            <i class="fas fa-calendar-alt text-blue-600 mr-2"></i>
                                                            <h4 class="text-sm font-semibold text-gray-800">Trip Details</h4>
                                                        </div>
                                                        <div class="bg-white p-3 rounded-md shadow-sm border border-gray-200 mb-3">
                                                            <div class="flex items-center">
                                                                <i class="fas fa-calendar-check text-gray-500 mr-2 text-xs"></i>
                                                                <p class="text-xs font-medium text-gray-700">Check-in and Check-out:</p>
                                                            </div>
                                                            <p class="text-xs text-gray-600 mt-1 pl-5"><?= (new DateTime($checkIn))->format('F d, Y'); ?> to <?= (new DateTime($checkout))->format('F d, Y'); ?></p>
                                                        </div>
                                                        <div class="grid grid-cols-3 gap-3">
                                                            <div class="bg-white p-3 rounded-md shadow-sm border border-gray-200">
                                                                <div class="flex items-center">
                                                                    <i class="fas fa-user-friends text-gray-500 mr-2 text-xs"></i>
                                                                    <p class="text-xs font-medium text-gray-700">Adults:</p>
                                                                </div>
                                                                <p class="text-sm font-semibold text-center mt-1 text-blue-600"><?= $adultCount; ?></p>
                                                            </div>
                                                            <div class="bg-white p-3 rounded-md shadow-sm border border-gray-200">
                                                                <div class="flex items-center">
                                                                    <i class="fas fa-child text-gray-500 mr-2 text-xs"></i>
                                                                    <p class="text-xs font-medium text-gray-700">Children:</p>
                                                                </div>
                                                                <p class="text-sm font-semibold text-center mt-1 text-blue-600"><?= $childrenCount; ?></p>
                                                            </div>
                                                            <div class="bg-white p-3 rounded-md shadow-sm border border-gray-200">
                                                                <div class="flex items-center">
                                                                    <i class="fas fa-users text-gray-500 mr-2 text-xs"></i>
                                                                    <p class="text-xs font-medium text-gray-700">Crew:</p>
                                                                </div>
                                                                <p class="text-sm font-semibold text-center mt-1 text-blue-600"><?= $crewCount; ?></p>
                                                            </div>
                                                        </div>
                                                    </div>

                                                    <!-- Approval Status Section -->
                                                    <div class="bg-gray-50 p-4 rounded-lg border border-gray-200 md:col-span-2">
                                                        <div class="flex items-center mb-3">
                                                            <i class="fas fa-clipboard-check text-blue-600 mr-2"></i>
                                                            <h4 class="text-sm font-semibold text-gray-800">Approval Status</h4>
                                                        </div>
                                                        <div class="grid grid-cols-3 gap-3">
                                                            <?php
                                                            // Define status colors and icons
                                                            $statusColors = [
                                                                'Approved' => 'green',
                                                                'Waiting' => 'yellow',
                                                                'Declined' => 'red'
                                                            ];

                                                            $statusIcons = [
                                                                'Approved' => 'check-circle',
                                                                'Waiting' => 'clock',
                                                                'Declined' => 'times-circle'
                                                            ];

                                                            // Resort Status
                                                            $resortStatus = $row['resort'];
                                                            $resortColor = isset($statusColors[$resortStatus]) ? $statusColors[$resortStatus] : 'gray';
                                                            $resortIcon = isset($statusIcons[$resortStatus]) ? $statusIcons[$resortStatus] : 'question-circle';

                                                            // Boat Status
                                                            $boatStatus = $row['boat'];
                                                            $boatColor = isset($statusColors[$boatStatus]) ? $statusColors[$boatStatus] : 'gray';
                                                            $boatIcon = isset($statusIcons[$boatStatus]) ? $statusIcons[$boatStatus] : 'question-circle';

                                                            // Treasurer Status
                                                            $treasurerStatus = $row['treasurer'];
                                                            $treasurerColor = isset($statusColors[$treasurerStatus]) ? $statusColors[$treasurerStatus] : 'gray';
                                                            $treasurerIcon = isset($statusIcons[$treasurerStatus]) ? $statusIcons[$treasurerStatus] : 'question-circle';
                                                            ?>

                                                            <div class="bg-white p-3 rounded-md shadow-sm border border-gray-200">
                                                                <div class="flex items-center justify-between">
                                                                    <p class="text-xs font-medium text-gray-700">Resort Status:</p>
                                                                    <div class="w-6 h-6 rounded-full bg-<?= $resortColor; ?>-100 flex items-center justify-center">
                                                                        <i class="fas fa-<?= $resortIcon; ?> text-<?= $resortColor; ?>-600 text-xs"></i>
                                                                    </div>
                                                                </div>
                                                                <p class="text-xs font-semibold text-<?= $resortColor; ?>-600 mt-2 text-center"><?= $resortStatus; ?></p>
                                                            </div>

                                                            <div class="bg-white p-3 rounded-md shadow-sm border border-gray-200">
                                                                <div class="flex items-center justify-between">
                                                                    <p class="text-xs font-medium text-gray-700">Boat Status:</p>
                                                                    <div class="w-6 h-6 rounded-full bg-<?= $boatColor; ?>-100 flex items-center justify-center">
                                                                        <i class="fas fa-<?= $boatIcon; ?> text-<?= $boatColor; ?>-600 text-xs"></i>
                                                                    </div>
                                                                </div>
                                                                <p class="text-xs font-semibold text-<?= $boatColor; ?>-600 mt-2 text-center"><?= $boatStatus; ?></p>
                                                            </div>

                                                            <div class="bg-white p-3 rounded-md shadow-sm border border-gray-200">
                                                                <div class="flex items-center justify-between">
                                                                    <p class="text-xs font-medium text-gray-700">Treasurer Status:</p>
                                                                    <div class="w-6 h-6 rounded-full bg-<?= $treasurerColor; ?>-100 flex items-center justify-center">
                                                                        <i class="fas fa-<?= $treasurerIcon; ?> text-<?= $treasurerColor; ?>-600 text-xs"></i>
                                                                    </div>
                                                                </div>
                                                                <p class="text-xs font-semibold text-<?= $treasurerColor; ?>-600 mt-2 text-center"><?= $treasurerStatus; ?></p>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>

                                            <!-- Modal Footer -->
                                            <div class="p-4 bg-gray-50 border-t border-gray-200 rounded-b-lg">
                                                <div class="flex justify-end">
                                                    <button data-modal-hide="view-approvals-modal-<?= $row['booking_id']; ?>" type="button" class="action-button btn-close pending">
                                                        <i class="fas fa-times-circle mr-1.5"></i> Close
                                                    </button>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                        <?php
                            }
                        }
                    } catch (PDOException $e) {
                        ?>
                        <tr>
                            <td colspan="5" class="px-6 py-8 text-center">
                                <div class="flex flex-col items-center justify-center">
                                    <div class="bg-red-100 p-4 rounded-full mb-4">
                                        <i class="fas fa-exclamation-triangle text-red-400 text-2xl"></i>
                                    </div>
                                    <h3 class="text-lg font-semibold text-gray-900 mb-2">Error Loading Bookings</h3>
                                    <p class="text-gray-500 mb-4">There was an error loading the monitoring data:
                                        <?= htmlspecialchars($e->getMessage()); ?>
                                    </p>
                                    <button onclick="window.location.reload()" class="action-button btn-cancel">
                                        <i class="fas fa-refresh mr-2"></i>
                                        Try Again
                                    </button>
                                </div>
                            </td>
                        </tr>
                    <?php
                    }

                    if (empty($bookingDetails)) {
                    ?>
                        <tr>
                            <td colspan="5" class="px-6 py-12 text-center">
                                <div class="flex flex-col items-center justify-center">
                                    <div class="bg-purple-100 p-4 rounded-full mb-4">
                                        <i class="fas fa-chart-line text-purple-500 text-2xl"></i>
                                    </div>
                                    <h3 class="text-lg font-semibold text-gray-900 mb-2">No Bookings to Monitor</h3>
                                    <p class="text-gray-500 text-sm mb-4 max-w-sm">When bookings need monitoring, they will appear here for review and management.</p>
                                    <a href="home.php" class="action-button btn-view">
                                        <i class="fas fa-arrow-left mr-2"></i>
                                        Back to Dashboard
                                    </a>
                                </div>
                            </td>
                        </tr>
                    <?php
                    }
                    ?>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>


<?php
require '_footer.php';
?>