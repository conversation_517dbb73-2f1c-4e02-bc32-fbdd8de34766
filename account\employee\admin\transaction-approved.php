<?php
require '_header.php';
?>

<link rel="stylesheet" href="assets/transaction-styles.css">

<div class="p-4 sm:ml-64">
    <div class="mt-16 fade-in"> <!-- Adjusted for top navbar -->
        <!-- Header Section -->
        <div class="bg-gradient-to-r from-green-600 to-green-700 rounded-xl shadow-lg p-6 mb-8">
            <!-- Breadcrumb -->
            <nav class="flex mb-4" aria-label="Breadcrumb">
                <ol class="inline-flex items-center space-x-1 md:space-x-2">
                    <li class="inline-flex items-center">
                        <a href="home.php"
                            class="inline-flex items-center text-sm font-medium text-green-100 hover:text-white transition-colors">
                            <svg class="w-4 h-4 me-2" fill="currentColor" viewBox="0 0 20 20">
                                <path
                                    d="m19.707 9.293-2-2-7-7a1 1 0 0 0-1.414 0l-7 7-2 2a1 1 0 0 0 1.414 1.414L2 10.414V18a2 2 0 0 0 2 2h3a1 1 0 0 0 1-1v-4a1 1 0 0 1 1-1h2a1 1 0 0 1 1 1v4a1 1 0 0 0 1 1h3a2 2 0 0 0 2-2v-7.586l.293.293a1 1 0 0 0 1.414-1.414Z" />
                            </svg>
                            Home
                        </a>
                    </li>
                    <li>
                        <div class="flex items-center">
                            <svg class="w-3 h-3 text-green-200 mx-1" fill="none" viewBox="0 0 6 10">
                                <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round"
                                    stroke-width="2" d="m1 9 4-4-4-4" />
                            </svg>
                            <span class="text-sm font-medium text-green-100">Transactions</span>
                        </div>
                    </li>
                    <li>
                        <div class="flex items-center">
                            <svg class="w-3 h-3 text-green-200 mx-1" fill="none" viewBox="0 0 6 10">
                                <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round"
                                    stroke-width="2" d="m1 9 4-4-4-4" />
                            </svg>
                            <span class="text-sm font-medium text-white">Approved</span>
                        </div>
                    </li>
                </ol>
            </nav>

            <!-- Title Section -->
            <div class="flex items-center">
                <div class="bg-white bg-opacity-20 p-3 rounded-xl mr-4">
                    <i class="fas fa-check-circle text-white text-2xl"></i>
                </div>
                <div>
                    <h1 class="text-3xl font-bold text-white mb-1">Approved Bookings</h1>
                    <p class="text-green-100">Bookings that have been approved by all operators</p>
                </div>
            </div>
        </div>

        <!-- Search and Filter Section -->
        <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-4 mb-6">
            <div class="flex flex-col sm:flex-row gap-4 items-center justify-between">
                <div class="relative flex-1 max-w-md">
                    <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                        <i class="fas fa-search text-gray-400"></i>
                    </div>
                    <input type="text" id="searchInput" placeholder="Search bookings..."
                        class="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500 transition-colors">
                </div>
                <div class="flex items-center gap-2 text-sm text-gray-600">
                    <i class="fas fa-info-circle"></i>
                    <span id="bookingCount">Loading bookings...</span>
                </div>
            </div>
        </div>
        <!-- Bookings Grid -->
        <div id="bookingsContainer" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            <?php
            try {
                $getMthoStatus = "Approved";
                $getBookingStatus = "approved";

                $bookingDetails = getBookingDetails($pdo, $getMthoStatus, $getBookingStatus);

                if ($bookingDetails && count($bookingDetails) > 0) {
                    foreach ($bookingDetails as $row) {
                        $booking_id = $row['booking_id'];
                        $operatorName = trim($row['firstname'] . ' ' . $row['middlename'] . ' ' . $row['lastname'] . ' ' . $row['extname']);
                        $designationTour = $row['tour_operator_designation'];
                        $destination = $row['resort_operator_designation'];
                        $referenceNumber = $row['referenceNum'];
                        ?>
                        <!-- Booking Card -->
                        <div class="booking-card rounded-lg p-6 fade-in"
                            data-reference="<?= htmlspecialchars($referenceNumber ?? ''); ?>"
                            data-operator="<?= htmlspecialchars($operatorName ?? ''); ?>"
                            data-designation="<?= htmlspecialchars($designationTour ?? ''); ?>"
                            data-destination="<?= htmlspecialchars($destination ?? ''); ?>" data-status="approved">
                            <!-- Card Header -->
                            <div class="flex justify-between items-start mb-4">
                                <div class="flex-1">
                                    <h3 class="text-lg font-semibold text-gray-900 mb-2">
                                        <?= htmlspecialchars($referenceNumber ?? 'N/A'); ?>
                                    </h3>
                                    <div class="status-badge status-approved">
                                        <i class="fas fa-check-circle mr-1"></i>
                                        Approved
                                    </div>
                                </div>
                            </div>

                            <!-- Card Content -->
                            <div class="space-y-3 mb-4">
                                <div class="flex items-start">
                                    <div class="flex-shrink-0 w-5 h-5 mt-0.5 flex items-center justify-center">
                                        <i class="fas fa-user text-green-500"></i>
                                    </div>
                                    <div class="ml-3">
                                        <p class="text-sm font-medium text-gray-900">
                                            <?= htmlspecialchars($operatorName ?? 'N/A'); ?></p>
                                        <p class="text-xs text-gray-500">Tour Operator</p>
                                    </div>
                                </div>

                                <div class="flex items-start">
                                    <div class="flex-shrink-0 w-5 h-5 mt-0.5 flex items-center justify-center">
                                        <i class="fas fa-briefcase text-green-500"></i>
                                    </div>
                                    <div class="ml-3">
                                        <p class="text-sm font-medium text-gray-900">
                                            <?= htmlspecialchars($designationTour ?? 'N/A'); ?></p>
                                        <p class="text-xs text-gray-500">Designation</p>
                                    </div>
                                </div>

                                <div class="flex items-start">
                                    <div class="flex-shrink-0 w-5 h-5 mt-0.5 flex items-center justify-center">
                                        <i class="fas fa-map-marker-alt text-green-500"></i>
                                    </div>
                                    <div class="ml-3">
                                        <p class="text-sm font-medium text-gray-900"><?= htmlspecialchars($destination ?? 'N/A'); ?>
                                        </p>
                                        <p class="text-xs text-gray-500">Destination</p>
                                    </div>
                                </div>
                            </div>

                            <!-- Card Actions -->
                            <div class="flex justify-end">
                                <a href="view-approved-transaction.php?id=<?= $booking_id; ?>"
                                    class="action-button bg-green-600 hover:bg-green-700 text-white">
                                    <i class="fas fa-eye mr-1.5"></i>
                                    View Details
                                </a>
                            </div>
                        </div>
                        <?php
                    }
                } else {
                    echo '<div class="col-span-full text-center py-8 bg-gray-50 rounded-lg border border-gray-200">
                            <div class="flex flex-col items-center justify-center">
                                <i class="fas fa-check-circle text-gray-400 text-4xl mb-3"></i>
                                <h3 class="text-lg font-medium text-gray-900 mb-1">No Approved Bookings</h3>
                                <p class="text-gray-500">There are no approved bookings at the moment.</p>
                            </div>
                          </div>';
                }
            } catch (PDOException $e) {
                echo '<div class="col-span-full text-center py-8 bg-red-50 rounded-lg border border-red-200">
                        <div class="flex flex-col items-center justify-center">
                            <i class="fas fa-exclamation-circle text-red-500 text-4xl mb-3"></i>
                            <h3 class="text-lg font-medium text-red-800 mb-1">Error</h3>
                            <p class="text-red-600">' . $e->getMessage() . '</p>
                        </div>
                      </div>';
            }
            ?>
        </div>
    </div>
</div>


<?php
require '_footer.php';
?>