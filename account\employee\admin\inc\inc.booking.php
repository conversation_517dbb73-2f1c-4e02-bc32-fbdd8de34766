<?php
session_start();
include '../../../../connection/dbconnect.php';

if ($_SERVER["REQUEST_METHOD"] == "POST") {
    if (isset($_POST['mthoResponseBtn'])) {
        try {

            if (!isset($_POST['csrf_token']) || $_POST['csrf_token'] !== $_SESSION['csrf_token']) {
                throw new Exception("Invalid CSRF token.");
            }
            if (!isset($_SESSION['id'])) {
                throw new Exception("User not authenticated");
            }

            // Validate Booking ID
            $getBookingId = $_POST['booking_id'] ?? null;
            if (!$getBookingId) {
                throw new Exception("Invalid booking ID.");
            }
            $mthoResponse = trim($_POST['mthoResponse']);
            $referenceNumber = trim($_POST['referenceNumber']);
            $ctrlNumber = trim($_POST['ctrlNum']);

            if ($ctrlNumber === 0) {
                $setCtrlNum = null;
            } else {
                $setCtrlNum = $ctrlNumber;
            }

            // Start transaction
            $pdo->beginTransaction();

            $stmt = $pdo->prepare("UPDATE cb_booking_approvals SET mtho = :mthoResponse,
                             date_updated = NOW() 
                             WHERE booking_id = :booking_id");
            $stmt->execute([
                ':mthoResponse' => $mthoResponse,
                ':booking_id' => $getBookingId
            ]);

            // Update Approvals           
            if ($mthoResponse === "Approved") {
                $stmt = $pdo->prepare("UPDATE cb_bookings SET control_number = :controlNumber, booking_status = :booking_status WHERE booking_id = :booking_id");
                $stmt->execute([
                    ':controlNumber' => $setCtrlNum,
                    ':booking_status' => "approved",
                    ':booking_id' => $getBookingId
                ]);
            } else {
                $stmt = $pdo->prepare("UPDATE cb_bookings 
                SET booking_status = :booking_status 
                WHERE booking_id = :booking_id");
                $stmt->execute([
                    ':booking_status' => "declined",
                    ':booking_id' => $getBookingId
                ]);

                $stmt = $pdo->prepare(
                    "SELECT * FROM cb_bookings WHERE booking_id = :booking_id"
                );
                $stmt->execute([':booking_id' => $getBookingId]);
                $booking = $stmt->fetch(PDO::FETCH_ASSOC);

                if (!$booking) {
                    throw new Exception('Booking not found.');
                }

                // Insert booking data into cb_booking_cancellation table with status 'declined'
                $stmt = $pdo->prepare(
                    "INSERT INTO cb_booking_cancellation
                    (booking_id, referenceNum, tour_operator_id, resort_operator_id, boat_id, port_id,
                    check_in_date, check_out_date, booking_status, created_by, date_created)
                    VALUES
                    (:booking_id, :referenceNum, :tour_operator_id, :resort_operator_id, :boat_id, :port_id,
                    :check_in_date, :check_out_date, 'declined', :created_by, NOW())"
                );

                $stmt->execute([
                    ':booking_id' => $booking['booking_id'],
                    ':referenceNum' => $booking['referenceNum'],
                    ':tour_operator_id' => $booking['tour_operator_id'],
                    ':resort_operator_id' => $booking['resort_operator_id'],
                    ':boat_id' => $booking['boat_id'],
                    ':port_id' => $booking['port_id'],
                    ':check_in_date' => $booking['check_in_date'],
                    ':check_out_date' => $booking['check_out_date'],
                    ':created_by' => $_SESSION['id']
                ]);
            }


            // Insert log entry
            $type = "Booking - " . $mthoResponse;
            $description = "MTHO: " . $_SESSION['username'] . " " . $mthoResponse . " a booking: " . $referenceNumber . ". Date Created: " . date("Y-m-d");
            $stmt = $pdo->prepare("INSERT INTO system_logs (type, description) VALUES (:type, :description)");
            $stmt->execute([
                ':type' => $type,
                ':description' => $description
            ]);


            // Commit transaction
            $pdo->commit();

            $_SESSION['success'] = $mthoResponse . " successfully";
            header("Location: ../transaction-approved.php");
            exit();
        } catch (Exception $e) {
            $pdo->rollBack();
            $_SESSION['error'] = $e->getMessage();
            header("Location: ../transaction-pending.php");
            exit();
        }
    }
}


if ($_SERVER["REQUEST_METHOD"] == "POST") {
    if (isset($_POST['departBookingBtn'])) {
        try {

            if (!isset($_POST['csrf_token']) || $_POST['csrf_token'] !== $_SESSION['csrf_token']) {
                throw new Exception("Invalid CSRF token.");
            }
            if (!isset($_SESSION['id'])) {
                throw new Exception("User not authenticated");
            }

            // Validate Booking ID
            $getBookingId = $_POST['booking_id'] ?? null;
            if (!$getBookingId) {
                throw new Exception("Invalid booking ID.");
            }
            $booking_status = trim($_POST['booking_status']);
            $referenceNumber = trim($_POST['referenceNumber']);

            // Start transaction
            $pdo->beginTransaction();

            // Update Approvals
            $stmt = $pdo->prepare("UPDATE cb_bookings SET booking_status = :booking_status WHERE booking_id = :booking_id");
            $stmt->execute([
                ':booking_status' => $booking_status,
                ':booking_id' => $getBookingId
            ]);

            // Insert log entry
            $type = "Booking - " . $booking_status;
            $description = "MTHO: " . $_SESSION['username'] . " " . $booking_status . " a booking: " . $referenceNumber . ". Date Created: " . date("Y-m-d");
            $stmt = $pdo->prepare("INSERT INTO system_logs (type, description) VALUES (:type, :description)");
            $stmt->execute([
                ':type' => $type,
                ':description' => $description
            ]);


            // Commit transaction
            $pdo->commit();

            $_SESSION['success'] = $mthoResponse . " successfully";
            header("Location: ../transaction-approved.php");
            exit();
        } catch (Exception $e) {
            $pdo->rollBack();
            $_SESSION['error'] = $e->getMessage();
            header("Location: ../view-approved-transaction.php?id=" . $getBookingId);
            exit();
        }
    }
}
