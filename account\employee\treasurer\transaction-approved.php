<?php
require '_header.php';
?>

<link rel="stylesheet" href="assets/transaction-styles.css">

<div class="p-4 sm:ml-64">
    <div class="mt-16 fade-in"> <!-- Adjusted for top navbar -->
        <!-- Header Section -->
        <div class="bg-gradient-to-r from-green-600 to-green-700 rounded-xl shadow-lg p-6 mb-8">
            <!-- Breadcrumb -->
            <nav class="flex mb-4" aria-label="Breadcrumb">
                <ol class="inline-flex items-center space-x-1 md:space-x-2">
                    <li class="inline-flex items-center">
                        <a href="home.php" class="inline-flex items-center text-sm font-medium text-green-100 hover:text-white transition-colors">
                            <svg class="w-4 h-4 me-2" fill="currentColor" viewBox="0 0 20 20">
                                <path d="m19.707 9.293-2-2-7-7a1 1 0 0 0-1.414 0l-7 7-2 2a1 1 0 0 0 1.414 1.414L2 10.414V18a2 2 0 0 0 2 2h3a1 1 0 0 0 1-1v-4a1 1 0 0 1 1-1h2a1 1 0 0 1 1 1v4a1 1 0 0 0 1 1h3a2 2 0 0 0 2-2v-7.586l.293.293a1 1 0 0 0 1.414-1.414Z" />
                            </svg>
                            Home
                        </a>
                    </li>
                    <li>
                        <div class="flex items-center">
                            <svg class="w-3 h-3 text-green-200 mx-1" fill="none" viewBox="0 0 6 10">
                                <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="m1 9 4-4-4-4" />
                            </svg>
                            <span class="text-sm font-medium text-green-100">Transactions</span>
                        </div>
                    </li>
                    <li>
                        <div class="flex items-center">
                            <svg class="w-3 h-3 text-green-200 mx-1" fill="none" viewBox="0 0 6 10">
                                <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="m1 9 4-4-4-4" />
                            </svg>
                            <span class="text-sm font-medium text-white">Approved</span>
                        </div>
                    </li>
                </ol>
            </nav>

            <!-- Title Section -->
            <div class="flex items-center">
                <div class="bg-white bg-opacity-20 p-3 rounded-xl mr-4">
                    <i class="fas fa-check-circle text-white text-2xl"></i>
                </div>
                <div>
                    <h1 class="text-3xl font-bold text-white mb-1">Approved Bookings</h1>
                    <p class="text-green-100">View details of approved bookings</p>
                </div>
            </div>
        </div>

        <!-- Search and Filter Section -->
        <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-4 mb-6">
            <div class="flex flex-col sm:flex-row gap-4 items-center justify-between">
                <div class="relative flex-1 max-w-md">
                    <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                        <i class="fas fa-search text-gray-400"></i>
                    </div>
                    <input type="text" id="searchInput" placeholder="Search bookings..." class="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500 transition-colors">
                </div>
                <div class="flex items-center gap-2 text-sm text-gray-600">
                    <i class="fas fa-info-circle"></i>
                    <span id="bookingCount">Loading bookings...</span>
                </div>
            </div>
        </div>

        <!-- Bookings Grid -->
        <div id="bookings-container" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 fade-in">
            <?php
            try {
                $sql = "SELECT * FROM cb_treasurer_record ORDER BY id DESC ";

                // Prepare and execute the statement
                $stmt = $pdo->prepare($sql);
                $stmt->execute();

                // Fetch the results
                $rows = $stmt->fetchAll(PDO::FETCH_ASSOC);

                if ($rows) {
                    foreach ($rows as $row) {
                        $referenceNumber = $row['referenceNum'];
                        $operatorName = $row['operatorName'];
                        $designation = $row['designation'];
                        $adultCount = $row['adultCount'];
                        $childrenCount = $row['childrenCount'];
                        $totalPax = $adultCount + $childrenCount;
                        $paymentMethod = $row['paymentMethod'];
                        $orNum = $row['orNumber'];
                        $totalAmount = $row['totalAmount'];
                        $dateCreated = $row['date_created'];
                        $formattedDate = date("n/j/Y", strtotime($dateCreated));
                        
            ?>
                        <div class="booking-card bg-white rounded-xl shadow-lg border border-gray-200 p-6 hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1" data-reference="<?= htmlspecialchars($referenceNumber ?? ''); ?>" data-operator="<?= htmlspecialchars($operatorName ?? ''); ?>" data-status="approved">
                            <!-- Card Header -->
                            <div class="flex justify-between items-start mb-4">
                                <div class="flex-1">
                                    <h3 class="text-lg font-semibold text-gray-900 mb-2">
                                        <?= htmlspecialchars($referenceNumber ?? 'N/A'); ?>
                                    </h3>
                                    <div class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-green-100 text-green-800">
                                        <i class="fas fa-check-circle mr-1"></i>
                                        Approved
                                    </div>
                                </div>
                            </div>

                            <!-- Booking Details -->
                            <div class="space-y-3 mb-4">
                                <div class="flex items-center text-sm">
                                    <i class="fas fa-receipt text-gray-400 w-4 mr-3"></i>
                                    <span class="text-gray-600">O.R. Number:</span>
                                    <span class="ml-2 font-medium text-gray-900"><?= htmlspecialchars($orNum); ?></span>
                                </div>
                                <div class="flex items-center text-sm">
                                    <i class="fas fa-user-tie text-gray-400 w-4 mr-3"></i>
                                    <span class="text-gray-600">Operator:</span>
                                    <span class="ml-2 font-medium text-gray-900"><?= htmlspecialchars($operatorName); ?></span>
                                </div>
                                <div class="flex items-center text-sm">
                                    <i class="fas fa-building text-gray-400 w-4 mr-3"></i>
                                    <span class="text-gray-600">Designation:</span>
                                    <span class="ml-2 font-medium text-gray-900"><?= htmlspecialchars($designation); ?></span>
                                </div>
                                <div class="flex items-center text-sm">
                                    <i class="fas fa-users text-gray-400 w-4 mr-3"></i>
                                    <span class="text-gray-600">Total Pax:</span>
                                    <span class="ml-2 font-medium text-gray-900"><?= $totalPax; ?></span>
                                </div>
                                <div class="flex items-center text-sm">
                                    <i class="fas fa-peso-sign text-gray-400 w-4 mr-3"></i>
                                    <span class="text-gray-600">Amount:</span>
                                    <span class="ml-2 font-medium text-gray-900">₱<?= number_format($totalAmount, 2); ?></span>
                                </div>
                                <div class="flex items-center text-sm">
                                    <i class="fas fa-calendar text-gray-400 w-4 mr-3"></i>
                                    <span class="text-gray-600">Date:</span>
                                    <span class="ml-2 font-medium text-gray-900"><?= $formattedDate; ?></span>
                                </div>
                            </div>
                            
                            <!-- Action Buttons -->
                            <div class="flex gap-2 pt-4 border-t border-gray-100">
                                <button
                                    data-modal-target="view-booking-modal-<?= $row['id']; ?>"
                                    data-modal-toggle="view-booking-modal-<?= $row['id']; ?>"
                                    type="button"
                                    class="action-button btn-view flex-1"
                                >
                                    <i class="fas fa-eye mr-2"></i>
                                    View Details
                                </button>
                            </div>
                        </div>
                                <!-- View Booking Modal -->
                                <div id="view-booking-modal-<?= $row['id']; ?>" tabindex="-1" class="hidden overflow-y-auto overflow-x-hidden fixed top-0 right-0 left-0 z-50 justify-center items-center w-full md:inset-0 h-[calc(100%-1rem)] max-h-full">
                                    <div class="relative w-full max-w-4xl max-h-full">
                                        <!-- Modal Container -->
                                        <div class="relative bg-white rounded-lg shadow-lg border border-gray-200">
                                            <!-- Modal Header -->
                                            <div class="flex items-center justify-between p-4 bg-gradient-to-r from-green-600 to-green-800 rounded-t-lg">
                                                <div class="flex items-center">
                                                    <div class="bg-white bg-opacity-20 p-2 rounded-lg mr-3">
                                                        <i class="fas fa-eye text-white text-lg"></i>
                                                    </div>
                                                    <h3 class="text-xl font-semibold text-white">
                                                        Booking Details
                                                    </h3>
                                                </div>
                                                <button type="button" class="text-white bg-transparent hover:bg-white hover:bg-opacity-20 rounded-lg text-sm w-8 h-8 ms-auto inline-flex justify-center items-center transition-all duration-200" data-modal-hide="view-booking-modal-<?= $row['id']; ?>">
                                                    <svg class="w-3 h-3" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 14 14">
                                                        <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="m1 1 6 6m0 0 6 6M7 7l6-6M7 7l-6 6" />
                                                    </svg>
                                                    <span class="sr-only">Close modal</span>
                                                </button>
                                            </div>

                                            <!-- Reference Number Banner -->
                                            <div class="bg-green-50 border-b border-green-200 p-3">
                                                <div class="flex items-center justify-between">
                                                    <div class="flex items-center">
                                                        <i class="fas fa-hashtag text-green-600 mr-2"></i>
                                                        <span class="text-sm font-medium text-green-800">Reference Number:</span>
                                                    </div>
                                                    <span class="text-sm font-bold text-green-600"><?= $referenceNumber; ?></span>
                                                </div>
                                            </div>

                                            <!-- Modal Body -->
                                            <div class="p-3">
                                                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3">
                                                    <!-- Operator & Destination Information -->
                                                    <div class="bg-gray-50 p-3 rounded-lg border border-gray-200">
                                                        <div class="flex items-center mb-2">
                                                            <i class="fas fa-user-tie text-green-600 mr-2"></i>
                                                            <h4 class="text-sm font-semibold text-gray-800">Operator & Destination</h4>
                                                        </div>
                                                        <div class="space-y-2 text-xs">
                                                            <div class="flex justify-between">
                                                                <span class="text-gray-600">Tour Operator:</span>
                                                                <span class="font-medium text-gray-900"><?= $operatorName; ?></span>
                                                            </div>
                                                            <div class="flex justify-between">
                                                                <span class="text-gray-600">Designation:</span>
                                                                <span class="font-medium text-gray-900"><?= $designation; ?></span>
                                                            </div>
                                                        </div>
                                                    </div>

                                                    <!-- Passenger Information -->
                                                    <div class="bg-gray-50 p-3 rounded-lg border border-gray-200">
                                                        <div class="flex items-center mb-2">
                                                            <i class="fas fa-users text-green-600 mr-2"></i>
                                                            <h4 class="text-sm font-semibold text-gray-800">Passenger Information</h4>
                                                        </div>
                                                        <div class="space-y-2 text-xs">
                                                            <div class="flex justify-between">
                                                                <span class="text-gray-600">Adults:</span>
                                                                <span class="font-medium text-gray-900"><?= $adultCount; ?></span>
                                                            </div>
                                                            <div class="flex justify-between">
                                                                <span class="text-gray-600">Children:</span>
                                                                <span class="font-medium text-gray-900"><?= $childrenCount; ?></span>
                                                            </div>
                                                        </div>
                                                    </div>

                                                    <!-- Payment Information -->
                                                    <div class="bg-gray-50 p-3 rounded-lg border border-gray-200">
                                                        <div class="flex items-center mb-2">
                                                            <i class="fas fa-money-bill-wave text-green-600 mr-2"></i>
                                                            <h4 class="text-sm font-semibold text-gray-800">Payment Information</h4>
                                                        </div>
                                                        <div class="space-y-2 text-xs">
                                                            <div class="flex justify-between">
                                                                <span class="text-gray-600">Payment Method:</span>
                                                                <span class="font-medium text-gray-900"><?= $paymentMethod; ?></span>
                                                            </div>
                                                            <div class="flex justify-between">
                                                                <span class="text-gray-600">O.R. Number:</span>
                                                                <span class="font-medium text-gray-900"><?= $orNum; ?></span>
                                                            </div>
                                                            <div class="flex justify-between">
                                                                 <span class="text-gray-600">Total Amount:</span>
                                                                 <span class="font-bold text-lg text-green-600">₱ <?= $totalAmount; ?></span>
                                                             </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>

                                            <!-- Modal Footer -->
                                            <div class="flex justify-end items-center p-3 bg-gray-50 border-t border-gray-200 rounded-b-lg">
                                                <button data-modal-hide="view-booking-modal-<?= $row['id']; ?>" type="button" class="flex items-center py-2 px-4 text-sm text-white bg-green-600 rounded-md hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-green-400 transition-all duration-200">
                                                    <i class="fas fa-times-circle mr-1.5"></i> Close
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                        <?php
                    }
                } else {
                    // No approved bookings found
                    ?>
                    <div class="col-span-full">
                        <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-8 text-center">
                            <div class="max-w-md mx-auto">
                                <div class="bg-gray-100 p-4 rounded-full inline-block mb-4">
                                    <i class="fas fa-inbox text-gray-400 text-2xl"></i>
                                </div>
                                <h3 class="text-lg font-semibold text-gray-900 mb-2">No Approved Bookings</h3>
                                <p class="text-gray-500 mb-4">There are currently no approved bookings to display.</p>
                                <a href="home.php" class="inline-flex items-center px-4 py-2 bg-green-600 hover:bg-green-700 text-white font-medium rounded-lg transition-colors">
                                    <i class="fas fa-arrow-left mr-2"></i>
                                    Back to Dashboard
                                </a>
                            </div>
                        </div>
                    </div>
                    <?php
                }
            } catch (PDOException $e) {
                ?>
                <div class="col-span-full">
                    <div class="bg-white rounded-xl shadow-sm border border-red-200 p-8 text-center">
                        <div class="max-w-md mx-auto">
                            <div class="bg-red-100 p-4 rounded-full inline-block mb-4">
                                <i class="fas fa-exclamation-triangle text-red-400 text-2xl"></i>
                            </div>
                            <h3 class="text-lg font-semibold text-gray-900 mb-2">Error Loading Bookings</h3>
                            <p class="text-gray-500 mb-4">There was an error loading the approved bookings: <?= htmlspecialchars($e->getMessage()); ?></p>
                            <button onclick="window.location.reload()" class="inline-flex items-center px-4 py-2 bg-red-600 hover:bg-red-700 text-white font-medium rounded-lg transition-colors">
                                <i class="fas fa-refresh mr-2"></i>
                                Try Again
                            </button>
                        </div>
                    </div>
                </div>
                <?php
            }
            ?>
        </div>
    </div>

<script src="assets/transaction-scripts.js"></script>

<?php
require '_footer.php';
?>