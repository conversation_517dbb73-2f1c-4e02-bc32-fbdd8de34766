<?php
// Function to fetch all resort operator room list data
function fetchTableData($pdo, $tableName)
{
    try {
        // SQL Query to fetch all data from the given table
        $sql = "SELECT * FROM $tableName";
        $stmt = $pdo->prepare($sql);
        $stmt->execute();
        // Fetch all rows
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    } catch (Exception $e) {
        // Handle and log errors
        error_log("Error fetching table data: " . $e->getMessage());
        return [];
    }
}

function getBookingDetails(PDO $pdo, $getMthoStatus, $getBookingStatus)
{
    try {
        $sql = "SELECT 
                    bb.booking_id AS booking_id,
                    bb.booking_status AS booking_status,
                    bb.referenceNum,
                    bb.check_in_date,
                    bb.check_out_date,
                    bb.control_number,
                    oi.designation AS resort_operator_designation, 
                    oi2.designation AS tour_operator_designation,
                    oi2.user_id AS tour_operator_id, 
                    oi2.firstname, 
                    oi2.middlename, 
                    oi2.lastname, 
                    oi2.extname, 
                    bob.boatName,
                    bob.user_id AS boat_operator_id, 
                    pl.portName,
                    cp.total_adults,
                    cp.total_children,
                    cp.total_crew,
                    cp.voucher_use,
                    cp.or_num,
                    cp.total_amount,
                    cp.receipt_image,
                    cp.payment_method,
                    cp.payment_status,
                    cp.voucher_use,
                    cba.treasurer,
                    cba.resort,
                    cba.boat,
                    cv.voucher_vinzons,
                    cv.voucher_others
                FROM cb_bookings bb
                LEFT JOIN operator_info oi ON bb.resort_operator_id = oi.user_id
                LEFT JOIN operator_info oi2 ON bb.tour_operator_id = oi2.user_id
                LEFT JOIN boat_operator_boatlist bob ON bb.boat_id = bob.id
                LEFT JOIN port_list pl ON bb.port_id = pl.id
                LEFT JOIN cb_payments cp ON bb.booking_id = cp.booking_id
                LEFT JOIN cb_booking_approvals cba ON bb.booking_id = cba.booking_id
                LEFT JOIN cb_vouchers cv ON oi2.user_id = cv.operator_id
                WHERE cba.mtho = :mtho
                AND bb.booking_status = :booking_status";

        $stmt = $pdo->prepare($sql);
        $stmt->bindParam(':mtho', $getMthoStatus, PDO::PARAM_STR);
        $stmt->bindParam(':booking_status', $getBookingStatus, PDO::PARAM_STR);
        $stmt->execute();

        // Fetch all rows
        $rows = $stmt->fetchAll(PDO::FETCH_ASSOC);

        return $rows ?: []; // Return empty array if no rows found
    } catch (PDOException $e) {
        error_log("Database error: " . $e->getMessage());
        return [];
    }
}

function getBookingDetails2(PDO $pdo, $getBookingId)
{
    try {
        $sql = "SELECT 
                    bb.booking_id AS booking_id,
                    bb.booking_status AS booking_status,
                    bb.referenceNum,
                    bb.check_in_date,
                    bb.check_out_date,
                    oi.designation AS resort_operator_designation, 
                    oi2.designation AS tour_operator_designation,
                    oi2.user_id AS tour_operator_id, 
                    oi2.firstname, 
                    oi2.middlename, 
                    oi2.lastname, 
                    oi2.extname, 
                    bob.boatName,
                    bob.user_id AS boat_operator_id, 
                    pl.portName,
                    cp.total_adults,
                    cp.total_children,
                    cp.total_crew,
                    cp.voucher_use,
                    cp.or_num,
                    cp.receipt_image,
                    cp.payment_status,
                    cba.treasurer,
                    cba.mtho,
                    cv.voucher_vinzons,
                    cv.voucher_others
                FROM cb_bookings bb
                LEFT JOIN operator_info oi ON bb.resort_operator_id = oi.user_id
                LEFT JOIN operator_info oi2 ON bb.tour_operator_id = oi2.user_id
                LEFT JOIN boat_operator_boatlist bob ON bb.boat_id = bob.id
                LEFT JOIN port_list pl ON bb.port_id = pl.id
                LEFT JOIN cb_payments cp ON bb.booking_id = cp.booking_id
                LEFT JOIN cb_booking_approvals cba ON bb.booking_id = cba.booking_id
                LEFT JOIN cb_vouchers cv ON oi2.user_id = cv.operator_id
                WHERE bb.booking_id = :booking_id";

        $stmt = $pdo->prepare($sql);
        $stmt->bindParam(':booking_id', $getBookingId, PDO::PARAM_INT);
        $stmt->execute();

        $row = $stmt->fetch(PDO::FETCH_ASSOC); // Fetch single row

        return $row ?: []; // Return empty array if no result found
    } catch (PDOException $e) {
        error_log("Database error: " . $e->getMessage());
        return [];
    }
}

function getIncompleteBooking(PDO $pdo, $getBookingStatus, $id)
{
    try {
        $sql = "SELECT
                    cbc.booking_id AS booking_id,
                    cbc.referenceNum,
                    cbc.date_created,
                    oi.designation,
                    bob.boatName
                FROM cb_booking_cancellation cbc
                JOIN operator_info oi
                    ON cbc.resort_operator_id = oi.user_id
                JOIN boat_operator_boatlist bob
                    ON cbc.boat_id = bob.id
                WHERE cbc.booking_status = :booking_status                
                AND cbc.created_by = :created_by
                ORDER BY cbc.date_created DESC";

        $stmt = $pdo->prepare($sql);
        $stmt->bindParam(':booking_status', $getBookingStatus, PDO::PARAM_STR);
        $stmt->bindParam(':created_by', $id, PDO::PARAM_INT);
        $stmt->execute();

        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    } catch (PDOException $e) {
        error_log("Error fetching cancellation requests: " . $e->getMessage());
        return false;
    }
}

function getTouristAndCrewCounts(PDO $pdo, $getBookingId)
{
    $stmt = $pdo->prepare("
        SELECT 
            SUM(CASE WHEN age >= 9 AND info_type = 'tourist' THEN 1 ELSE 0 END) AS adult_count,
            SUM(CASE WHEN age <= 7 AND info_type = 'tourist' THEN 1 ELSE 0 END) AS children_count,
            COUNT(CASE WHEN info_type = 'crewTts' THEN 1 ELSE NULL END) AS crewTts_count,
            COUNT(CASE WHEN info_type = 'crewMbca' THEN 1 ELSE NULL END) AS crewMbca_count
        FROM cb_tourists
        WHERE info_type IN ('tourist', 'crewTts', 'crewMbca')
        AND booking_id = :booking_id
    ");
    $stmt->bindParam(':booking_id', $getBookingId, PDO::PARAM_INT);
    $stmt->execute();
    $result = $stmt->fetch(PDO::FETCH_ASSOC);

    return [
        'adults' => $result['adult_count'] ?? 0,
        'children' => $result['children_count'] ?? 0,
        'crewTts' => $result['crewTts_count'] ?? 0,
        'crewMbca' => $result['crewMbca_count'] ?? 0
    ];
}

function getDistinctTourist(PDO $pdo, $parameter, $getBookingId)
{
    // Make sure $parameter is a valid column name to prevent SQL injection
    $allowedColumns = ["contact_number", "address"];
    if (!in_array($parameter, $allowedColumns)) {
        // Handle invalid parameter error
        return false;
    }

    $sql = "SELECT DISTINCT $parameter FROM cb_tourists WHERE booking_id = :booking_id";
    $stmt = $pdo->prepare($sql);
    $stmt->bindParam(':booking_id', $getBookingId, PDO::PARAM_INT);
    $stmt->execute();
    $result = $stmt->fetchAll(PDO::FETCH_ASSOC);
    $stmt->closeCursor();

    return $result;
}

function getNotifications(PDO $pdo): array
{
    // Using MySQL's boolean-sum trick:
    $stmt = $pdo->prepare("
        SELECT 
            SUM(cba.mtho = 'Waiting' AND bb.booking_status = 'pending') AS mtho_waiting,
            SUM(cba.mtho = 'Pending') AS mtho_pending,
            SUM(cba.mtho = 'Approved' AND bb.booking_status = 'approved') AS mtho_approved
        FROM 
            cb_bookings bb
        LEFT JOIN 
            cb_booking_approvals cba ON bb.booking_id = cba.booking_id
    ");
    $stmt->execute();
    $result = $stmt->fetch(PDO::FETCH_ASSOC);

    return [
        'Pending' => (int) ($result['mtho_pending'] ?? 0),
        'Approved' => (int) ($result['mtho_approved'] ?? 0),
        'Waiting' => (int) ($result['mtho_waiting'] ?? 0)
    ];
}
