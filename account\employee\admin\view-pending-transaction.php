<?php
ob_start();
require '_header.php';

$getBookingId = isset($_GET['id']) ? (int) $_GET['id'] : 0;

$bookingDetails = getBookingDetails2($pdo, $getBookingId);

if (!empty($bookingDetails)) {
    $getId = $_SESSION['id'] ?? null; // Define $id properly

    if ($bookingDetails['mtho'] === "Waiting" || $bookingDetails['mtho'] === "Approved") {
        header("Location: transaction-pending.php");
        exit;
    } else {
        $extname = $bookingDetails['extname'] ?? '';
        $middlename = $bookingDetails['middlename'] ?? '';
        $operatorName = trim($bookingDetails['firstname'] . ' ' . $middlename . ' ' . $bookingDetails['lastname'] . ' ' . $extname);
        $designationTour = $bookingDetails['tour_operator_designation'];
        $resortName = $bookingDetails['resort_operator_designation'];
        $referenceNumber = $bookingDetails['referenceNum'];
        $boatName = $bookingDetails['boatName'];
        $portName = $bookingDetails['portName'];
        $checkIn = $bookingDetails['check_in_date'];
        $checkout = $bookingDetails['check_out_date'];
        $adultCount = $bookingDetails['total_adults'];
        $childrenCount = $bookingDetails['total_children'];
        $crewCount = $bookingDetails['total_crew'];
        $orNum = $bookingDetails['or_num'];
        $receipt = $bookingDetails['receipt_image'];
        $voucherUse = $bookingDetails['voucher_use'];
        $treasurer = $bookingDetails['treasurer'] ?? '';
        $mtho = $bookingDetails['mtho'] ?? '';
        $paymentStatus = $bookingDetails['payment_status'];

        // Format dates for display
        $formattedCheckIn = (new DateTime($checkIn))->format('F d, Y');
        $formattedCheckout = (new DateTime($checkout))->format('F d, Y');

        // Create voucher badge
        if ($paymentStatus === "voucher") {
            $textColor = "text-green-600";
            $voucherBadge = '<span class="bg-green-100 text-green-800 text-xs font-medium px-2.5 py-0.5 rounded-full">Yes</span>';
        } else {
            $textColor = "text-red-600";
            $voucherBadge = '<span class="bg-red-100 text-red-800 text-xs font-medium px-2.5 py-0.5 rounded-full">No</span>';
        }
    }
}

ob_end_flush();
?>

<div class="p-4 sm:ml-64">
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 mt-14 overflow-hidden">
        <!-- Breadcrumb -->
        <div class="bg-gray-50 border-b border-gray-200 px-4 py-2.5">
            <nav class="flex" aria-label="Breadcrumb">
                <ol class="inline-flex items-center space-x-1 md:space-x-2 rtl:space-x-reverse">
                    <li class="inline-flex items-center">
                        <a href="home.php" class="inline-flex items-center text-sm font-medium text-gray-700 hover:text-blue-600">
                            <svg class="w-3 h-3 me-2.5" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="currentColor" viewBox="0 0 20 20">
                                <path d="m19.707 9.293-2-2-7-7a1 1 0 0 0-1.414 0l-7 7-2 2a1 1 0 0 0 1.414 1.414L2 10.414V18a2 2 0 0 0 2 2h3a1 1 0 0 0 1-1v-4a1 1 0 0 1 1-1h2a1 1 0 0 1 1 1v4a1 1 0 0 0 1 1h3a2 2 0 0 0 2-2v-7.586l.293.293a1 1 0 0 0 1.414-1.414Z" />
                            </svg>
                            Home
                        </a>
                    </li>
                    <li>
                        <div class="flex items-center">
                            <svg class="rtl:rotate-180 w-3 h-3 text-gray-400 mx-1" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 6 10">
                                <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="m1 9 4-4-4-4" />
                            </svg>
                            <a href="transaction-pending.php" class="ms-1 text-sm font-medium text-gray-700 hover:text-blue-600 md:ms-2">Pending Transactions</a>
                        </div>
                    </li>
                    <li aria-current="page">
                        <div class="flex items-center">
                            <svg class="rtl:rotate-180 w-3 h-3 text-gray-400 mx-1" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 6 10">
                                <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="m1 9 4-4-4-4" />
                            </svg>
                            <span class="ms-1 text-sm font-medium text-gray-500 md:ms-2">Transaction Details</span>
                        </div>
                    </li>
                </ol>
            </nav>
        </div>

        <div class="p-5">
            <!-- Header with Reference Number -->
            <div class="flex flex-col sm:flex-row sm:items-center justify-between mb-6 pb-4 border-b border-gray-200">
                <div class="flex items-center mb-3 sm:mb-0">
                    <div class="bg-blue-100 p-2 rounded-lg mr-3">
                        <i class="fas fa-file-invoice text-blue-600 text-xl"></i>
                    </div>
                    <div>
                        <h2 class="text-xl font-bold text-gray-900">Transaction Details</h2>
                        <p class="text-sm text-gray-600">Review booking information before approval</p>
                    </div>
                </div>
                <div class="bg-blue-50 px-4 py-2 rounded-lg border border-blue-200 inline-flex items-center">
                    <span class="text-sm font-medium text-gray-600 mr-2">Reference:</span>
                    <span class="text-base font-semibold text-blue-700"><?= $referenceNumber; ?></span>
                </div>
            </div>

            <!-- Main Content Grid -->
            <div class="grid grid-cols-1 lg:grid-cols-3 gap-5">
                <!-- Left Column: Booking Details -->
                <div class="lg:col-span-2 space-y-5">
                    <!-- Booking Information Card -->
                    <div class="bg-white rounded-lg border border-gray-200 overflow-hidden">
                        <div class="bg-gradient-to-r from-blue-50 to-blue-100 px-4 py-3 border-b border-gray-200">
                            <div class="flex items-center">
                                <i class="fas fa-info-circle text-blue-600 mr-2"></i>
                                <h3 class="font-semibold text-gray-800">Booking Information</h3>
                            </div>
                        </div>
                        
                        <div class="p-4">
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                <!-- Tour Operator Info -->
                                <div class="flex items-start">
                                    <div class="bg-blue-100 p-2 rounded-full mr-3 mt-1">
                                        <i class="fas fa-user-tie text-blue-600 text-sm"></i>
                                    </div>
                                    <div>
                                        <h4 class="text-sm font-medium text-gray-700">Tour Operator</h4>
                                        <p class="text-sm text-gray-800 mt-1"><?= htmlspecialchars($operatorName); ?></p>
                                        <p class="text-xs text-gray-500 mt-0.5"><?= htmlspecialchars($designationTour); ?></p>
                                    </div>
                                </div>
                                
                                <!-- Resort Info -->
                                <div class="flex items-start">
                                    <div class="bg-green-100 p-2 rounded-full mr-3 mt-1">
                                        <i class="fas fa-hotel text-green-600 text-sm"></i>
                                    </div>
                                    <div>
                                        <h4 class="text-sm font-medium text-gray-700">Resort</h4>
                                        <p class="text-sm text-gray-800 mt-1"><?= htmlspecialchars($resortName); ?></p>
                                    </div>
                                </div>
                                
                                <!-- Transportation Info -->
                                <div class="flex items-start">
                                    <div class="bg-indigo-100 p-2 rounded-full mr-3 mt-1">
                                        <i class="fas fa-ship text-indigo-600 text-sm"></i>
                                    </div>
                                    <div>
                                        <h4 class="text-sm font-medium text-gray-700">Transportation</h4>
                                        <p class="text-sm text-gray-800 mt-1"><?= htmlspecialchars($boatName); ?></p>
                                        <p class="text-xs text-gray-500 mt-0.5">Port: <?= htmlspecialchars($portName); ?></p>
                                    </div>
                                </div>
                                
                                <!-- Trip Dates -->
                                <div class="flex items-start">
                                    <div class="bg-purple-100 p-2 rounded-full mr-3 mt-1">
                                        <i class="fas fa-calendar-alt text-purple-600 text-sm"></i>
                                    </div>
                                    <div>
                                        <h4 class="text-sm font-medium text-gray-700">Trip Dates</h4>
                                        <p class="text-sm text-gray-800 mt-1"><?= $formattedCheckIn; ?> - <?= $formattedCheckout; ?></p>
                                        <div class="flex items-center mt-1">
                                            <span class="text-xs text-gray-500 mr-2">Voucher:</span>
                                            <?= $voucherBadge; ?>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- OR Number Section (if available) -->
                            <?php if (!empty($orNum)): ?>
                            <div class="mt-4 pt-4 border-t border-gray-100">
                                <div class="flex flex-col sm:flex-row sm:items-center justify-between">
                                    <div class="flex items-center">
                                        <div class="bg-amber-100 p-2 rounded-full mr-3">
                                            <i class="fas fa-file-invoice-dollar text-amber-600 text-sm"></i>
                                        </div>
                                        <div>
                                            <h4 class="text-sm font-medium text-gray-700">O.R. Number</h4>
                                            <p class="text-sm text-gray-800 mt-1"><?= htmlspecialchars($orNum); ?></p>
                                        </div>
                                    </div>

                                    <?php if (!empty($receipt)): ?>
                                    <div class="mt-3 sm:mt-0">
                                        <a href="../../operator/receipt/<?= $receipt; ?>" class="inline-flex items-center text-blue-600 hover:text-blue-800 text-sm font-medium" target="_blank" rel="noopener noreferrer">
                                            <i class="fas fa-receipt mr-2"></i> View Receipt
                                        </a>
                                    </div>
                                    <?php endif; ?>
                                </div>
                            </div>
                            <?php endif; ?>
                        </div>
                    </div>
                    
                    <!-- Passenger List Card -->
                    <div class="bg-white rounded-lg border border-gray-200 overflow-hidden">
                        <div class="bg-gradient-to-r from-blue-50 to-blue-100 px-4 py-3 border-b border-gray-200">
                            <div class="flex items-center">
                                <i class="fas fa-users text-blue-600 mr-2"></i>
                                <h3 class="font-semibold text-gray-800">Passenger List</h3>
                            </div>
                        </div>
                        
                        <div class="overflow-x-auto">
                            <table id="search-table" class="min-w-full divide-y divide-gray-200">
                                <thead class="bg-gray-50">
                                    <tr>
                                        <th scope="col" class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase">Type</th>
                                        <th scope="col" class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase">Name</th>
                                        <th scope="col" class="px-4 py-3 text-center text-xs font-medium text-gray-500 uppercase">Action</th>
                                    </tr>
                                </thead>
                                <tbody class="divide-y divide-gray-200 bg-white">
                    <?php
                    try {
                        // SQL query to fetch booking details
                        $sql = "SELECT *
                                FROM cb_tourists
                                WHERE booking_id = :booking_id
                                ORDER BY
                                    CASE
                                        WHEN info_type = 'crewTts' THEN 1
                                        WHEN info_type = 'crewMbca' THEN 2
                                        ELSE 3
                                    END ASC,
                                    full_name ASC";
                        // Prepare and execute the statement
                        $stmt = $pdo->prepare($sql);
                        $stmt->bindParam(':booking_id', $getBookingId, PDO::PARAM_STR);
                        $stmt->execute();

                        // Fetch the results
                        $rows = $stmt->fetchAll(PDO::FETCH_ASSOC);

                        if ($rows) {
                            foreach ($rows as $row) {

                                if ($row['info_type'] == "tourist") {
                                    $infoTypeBadge = '
                                    <span class="bg-gradient-to-r from-green-100 to-green-50 text-green-800 text-xs font-medium me-2 px-3 py-1 rounded-full border border-green-200 shadow-sm">Tourist</span>
                                    ';
                                } elseif ($row['info_type'] == "crewTts") {
                                    $infoTypeBadge = '
                                    <span class="bg-gradient-to-r from-blue-100 to-blue-50 text-blue-800 text-xs font-medium me-2 px-3 py-1 rounded-full border border-blue-200 shadow-sm">Crew TTS</span>
                                    ';
                                } else {
                                    $infoTypeBadge = '
                                    <span class="bg-gradient-to-r from-red-100 to-red-50 text-red-800 text-xs font-medium me-2 px-3 py-1 rounded-full border border-red-200 shadow-sm">Crew MBCA</span>
                                    ';
                                }
                    ?>
                                        <tr class="hover:bg-gray-50">
                                            <td class="px-4 py-3 text-sm font-medium text-gray-900"><?= $infoTypeBadge; ?></td>
                                            <td class="px-4 py-3 text-sm text-gray-700"><?= htmlspecialchars($row['full_name']); ?></td>
                                            <td class="px-4 py-3 text-center">
                                                <button data-modal-target="view-passenger-modal-<?= $row['tourist_id']; ?>" data-modal-toggle="view-passenger-modal-<?= $row['tourist_id']; ?>" type="button" class="inline-flex items-center justify-center bg-blue-600 hover:bg-blue-700 text-white font-medium rounded-lg text-xs px-3 py-1.5 focus:outline-none focus:ring-4 focus:ring-blue-300 transition-colors duration-200">
                                                    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-4 h-4">
                                                        <path stroke-linecap="round" stroke-linejoin="round" d="M2.036 12.322a1.012 1.012 0 0 1 0-.639C3.423 7.51 7.36 4.5 12 4.5c4.638 0 8.573 3.007 9.963 7.178.07.207.07.431 0 .639C20.577 16.49 16.64 19.5 12 19.5c-4.638 0-8.573-3.007-9.963-7.178Z" />
                                                        <path stroke-linecap="round" stroke-linejoin="round" d="M15 12a3 3 0 1 1-6 0 3 3 0 0 1 6 0Z" />
                                                    </svg>
                                                </button>
                                            </td>
                                        </tr>
                                <!-- View Passenger Modal -->
                                <div id="view-passenger-modal-<?= $row['tourist_id']; ?>" tabindex="-1" class="hidden fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50">
                                    <div class="bg-white rounded-lg shadow-lg max-w-sm w-full mx-4">
                                        <!-- Modal header -->
                                        <div class="flex items-center justify-between p-4 border-b">
                                            <h3 class="text-lg font-semibold text-gray-900">Passenger Details</h3>
                                            <button type="button" class="text-gray-400 hover:text-gray-600" data-modal-hide="view-passenger-modal-<?= $row['tourist_id']; ?>">
                                                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                                                </svg>
                                            </button>
                                        </div>

                                        <!-- Modal Body -->
                                        <div class="p-4 space-y-3">
                                            <!-- Type Badge -->
                                            <?php if ($row['info_type'] == "tourist"): ?>
                                                <span class="inline-block bg-green-100 text-green-800 text-xs px-2 py-1 rounded-full">Tourist</span>
                                            <?php elseif ($row['info_type'] == "crewTts"): ?>
                                                <span class="inline-block bg-blue-100 text-blue-800 text-xs px-2 py-1 rounded-full">Crew TTS</span>
                                            <?php else: ?>
                                                <span class="inline-block bg-red-100 text-red-800 text-xs px-2 py-1 rounded-full">Crew MBCA</span>
                                            <?php endif; ?>

                                            <!-- Name -->
                                            <h4 class="text-lg font-medium text-gray-900"><?= htmlspecialchars($row['full_name']); ?></h4>
                                            
                                            <!-- Basic Info -->
                                            <div class="text-sm text-gray-600">
                                                <?= ucfirst($row['gender']); ?> • <?= htmlspecialchars($row['age']); ?> years old
                                            </div>

                                            <!-- Contact Info -->
                                            <div class="space-y-2">
                                                <?php if (!empty($row['contact_number'])): ?>
                                                <div>
                                                    <span class="text-xs text-gray-500">Phone:</span>
                                                    <p class="text-sm"><?= htmlspecialchars($row['contact_number']); ?></p>
                                                </div>
                                                <?php endif; ?>
                                                
                                                <?php if (!empty($row['address'])): ?>
                                                <div>
                                                    <span class="text-xs text-gray-500">Address:</span>
                                                    <p class="text-sm"><?= htmlspecialchars($row['demographic'] . ', ' . $row['address']); ?></p>
                                                </div>
                                                <?php endif; ?>
                                            </div>
                                        </div>

                                        <!-- Footer -->
                                        <div class="p-4 border-t">
                                            <button data-modal-hide="view-passenger-modal-<?= $row['tourist_id']; ?>" type="button" class="w-full px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 rounded-lg hover:bg-gray-200">
                                                Close
                                            </button>
                                        </div>
                                    </div>
                                </div>
                        <?php
                            }
                        }
                    } catch (PDOException $e) {
                        ?>
                                <tr>
                                    <td colspan="3" class="px-4 py-4 text-center text-sm text-red-600">
                                        Error: <?= htmlspecialchars($e->getMessage()); ?>
                                    </td>
                                </tr>
                    <?php
                    }
                    ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>

                <!-- Right Column: Summary & Actions -->
                <div class="space-y-5">
                    <!-- Passenger Count Summary Card -->
                    <div class="bg-white rounded-lg border border-gray-200 overflow-hidden">
                        <div class="bg-gradient-to-r from-blue-50 to-blue-100 px-4 py-3 border-b border-gray-200">
                            <div class="flex items-center">
                                <i class="fas fa-user-friends text-blue-600 mr-2"></i>
                                <h3 class="font-semibold text-gray-800">Passenger Summary</h3>
                            </div>
                        </div>
                        
                        <div class="p-4 space-y-3">
                            <!-- Adults Count -->
                            <div class="flex items-center justify-between p-2 bg-blue-50 rounded-lg">
                                <div class="flex items-center">
                                    <div class="bg-blue-100 p-1.5 rounded-full mr-3">
                                        <i class="fas fa-user text-blue-600 text-sm"></i>
                                    </div>
                                    <span class="text-sm font-medium text-gray-700">Adults</span>
                                </div>
                                <span class="text-lg font-semibold text-blue-700"><?= $adultCount; ?></span>
                            </div>
                            
                            <!-- Children Count -->
                            <div class="flex items-center justify-between p-2 bg-green-50 rounded-lg">
                                <div class="flex items-center">
                                    <div class="bg-green-100 p-1.5 rounded-full mr-3">
                                        <i class="fas fa-child text-green-600 text-sm"></i>
                                    </div>
                                    <span class="text-sm font-medium text-gray-700">Children</span>
                                </div>
                                <span class="text-lg font-semibold text-green-700"><?= $childrenCount; ?></span>
                            </div>
                            
                            <!-- Crew Count -->
                            <div class="flex items-center justify-between p-2 bg-purple-50 rounded-lg">
                                <div class="flex items-center">
                                    <div class="bg-purple-100 p-1.5 rounded-full mr-3">
                                        <i class="fas fa-ship text-purple-600 text-sm"></i>
                                    </div>
                                    <span class="text-sm font-medium text-gray-700">Crew</span>
                                </div>
                                <span class="text-lg font-semibold text-purple-700"><?= $crewCount; ?></span>
                            </div>
                            
                            <!-- Total Count -->
                            <div class="flex items-center justify-between p-2 bg-gray-100 rounded-lg mt-2 border-t-2 border-gray-200">
                                <div class="flex items-center">
                                    <div class="bg-gray-200 p-1.5 rounded-full mr-3">
                                        <i class="fas fa-users text-gray-700 text-sm"></i>
                                    </div>
                                    <span class="text-sm font-medium text-gray-700">Total</span>
                                </div>
                                <span class="text-lg font-semibold text-gray-800"><?= $adultCount + $childrenCount + $crewCount; ?></span>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Action Buttons Card -->
                    <div class="bg-white rounded-lg border border-gray-200 overflow-hidden">
                        <div class="bg-gradient-to-r from-blue-50 to-blue-100 px-4 py-3 border-b border-gray-200">
                            <div class="flex items-center">
                                <i class="fas fa-tasks text-blue-600 mr-2"></i>
                                <h3 class="font-semibold text-gray-800">Actions</h3>
                            </div>
                        </div>
                        
                        <div class="p-4 space-y-3">
                            <button data-modal-target="approve-modal" data-modal-toggle="approve-modal" class="w-full flex items-center justify-center px-4 py-2.5 text-sm font-medium text-white bg-green-600 rounded-lg hover:bg-green-700 focus:outline-none focus:ring-4 focus:ring-green-300 shadow-sm hover:shadow transition-all duration-300" type="button">
                                <i class="fas fa-check-circle mr-2"></i>
                                <span>Approve Transaction</span>
                            </button>
                            
                            <button data-modal-target="decline-modal" data-modal-toggle="decline-modal" class="w-full flex items-center justify-center px-4 py-2.5 text-sm font-medium text-white bg-red-600 rounded-lg hover:bg-red-700 focus:outline-none focus:ring-4 focus:ring-red-300 shadow-sm hover:shadow transition-all duration-300" type="button">
                                <i class="fas fa-times-circle mr-2"></i>
                                <span>Decline Transaction</span>
                            </button>
                            
                            <a href="transaction-pending.php" class="w-full flex items-center justify-center px-4 py-2.5 text-sm font-medium text-gray-700 bg-gray-100 rounded-lg hover:bg-gray-200 focus:outline-none focus:ring-4 focus:ring-gray-300 shadow-sm hover:shadow transition-all duration-300">
                                <i class="fas fa-arrow-left mr-2"></i>
                                <span>Back to Transactions</span>
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

        <!-- Decline Modal -->
        <div id="decline-modal" tabindex="-1" class="hidden fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50">
            <div class="bg-white rounded-lg shadow-lg max-w-sm w-full mx-4">
                <!-- Modal header -->
                <div class="flex items-center justify-between p-4 border-b">
                    <h3 class="text-lg font-semibold text-gray-900">Decline Transaction</h3>
                    <button type="button" class="text-gray-400 hover:text-gray-600" data-modal-hide="decline-modal">
                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                        </svg>
                    </button>
                </div>

                <form action="inc/inc.booking.php" method="POST">
                    <div class="p-4">
                        <input type="hidden" name="csrf_token" value="<?= htmlspecialchars($_SESSION['csrf_token'], ENT_QUOTES, 'UTF-8'); ?>">
                        <input type="hidden" value="<?= $getBookingId; ?>" name="booking_id">
                        <input type="hidden" value="Declined" name="mthoResponse">
                        <input type="hidden" value="<?= $referenceNumber; ?>" name="referenceNumber">
                        <input type="hidden" value="0" name="ctrlNum">

                        <!-- Warning Message -->
                        <div class="p-3 mb-4 text-sm text-red-800 bg-red-50 rounded-lg border border-red-200">
                            <p class="font-medium">Are you sure you want to decline this transaction?</p>
                            <p class="text-xs mt-1">This action cannot be undone.</p>
                        </div>

                        <!-- Transaction Info -->
                        <div class="space-y-2 mb-4">
                            <div>
                                <span class="text-xs text-gray-500">Reference:</span>
                                <p class="text-sm font-medium"><?= htmlspecialchars($referenceNumber); ?></p>
                            </div>
                            <div>
                                <span class="text-xs text-gray-500">Operator:</span>
                                <p class="text-sm"><?= htmlspecialchars($operatorName); ?></p>
                            </div>
                        </div>
                    </div>

                    <!-- Form Buttons -->
                    <div class="p-4 border-t flex space-x-2">
                        <button data-modal-hide="decline-modal" type="button" class="flex-1 px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 rounded-lg hover:bg-gray-200">
                            Cancel
                        </button>
                        <button type="submit" name="mthoResponseBtn" class="flex-1 px-4 py-2 text-sm font-medium text-white bg-red-600 rounded-lg hover:bg-red-700">
                            Decline
                        </button>
                    </div>
                </form>
            </div>
        </div>
        <!-- Approve Modal -->
        <div id="approve-modal" tabindex="-1" class="hidden fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50">
            <div class="bg-white rounded-lg shadow-lg max-w-sm w-full mx-4">
                <!-- Modal header -->
                <div class="flex items-center justify-between p-4 border-b">
                    <h3 class="text-lg font-semibold text-gray-900">Approve Transaction</h3>
                    <button type="button" class="text-gray-400 hover:text-gray-600" data-modal-hide="approve-modal">
                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                        </svg>
                    </button>
                </div>

                <form action="inc/inc.booking.php" method="POST">
                    <div class="p-4">
                        <input type="hidden" name="csrf_token" value="<?= htmlspecialchars($_SESSION['csrf_token'], ENT_QUOTES, 'UTF-8'); ?>">
                        <input type="hidden" value="<?= $getBookingId; ?>" name="booking_id">
                        <input type="hidden" value="Approved" name="mthoResponse">
                        <input type="hidden" value="<?= $referenceNumber; ?>" name="referenceNumber">

                        <!-- Success Message -->
                        <div class="p-3 mb-4 text-sm text-green-800 bg-green-50 rounded-lg border border-green-200">
                            <p class="font-medium">Please enter a control number to approve this transaction.</p>
                        </div>

                        <!-- Control Number Input -->
                        <div class="mb-4">
                            <label for="ctrlNum" class="block mb-2 text-sm font-medium text-gray-900">
                                Control Number <span class="text-red-500">*</span>
                            </label>
                            <input type="text" id="ctrlNum" name="ctrlNum" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500" placeholder="Enter control number" required>
                        </div>

                        <!-- Transaction Info -->
                        <div class="space-y-2 mb-4">
                            <div>
                                <span class="text-xs text-gray-500">Reference:</span>
                                <p class="text-sm font-medium"><?= htmlspecialchars($referenceNumber); ?></p>
                            </div>
                            <div>
                                <span class="text-xs text-gray-500">Operator:</span>
                                <p class="text-sm"><?= htmlspecialchars($operatorName); ?></p>
                            </div>
                        </div>
                    </div>

                    <!-- Form Buttons -->
                    <div class="p-4 border-t flex space-x-2">
                        <button data-modal-hide="approve-modal" type="button" class="flex-1 px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 rounded-lg hover:bg-gray-200">
                            Cancel
                        </button>
                        <button type="submit" name="mthoResponseBtn" class="flex-1 px-4 py-2 text-sm font-medium text-white bg-green-600 rounded-lg hover:bg-green-700">
                            Approve
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>


<?php
require '_footer.php';
?>